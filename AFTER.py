import bpy, mathutils

#initialize effector_passthrough node group
def effector_passthrough_node_group():
    effector_passthrough = bpy.data.node_groups.new(type = 'GeometryNodeTree', name = "Effector_Passthrough")

    effector_passthrough.color_tag = 'NONE'
    effector_passthrough.description = ""
    effector_passthrough.default_group_node_width = 140
    


    #effector_passthrough interface
    #Socket Geometry
    geometry_socket = effector_passthrough.interface.new_socket(name = "Geometry", in_out='OUTPUT', socket_type = 'NodeSocketGeometry')
    geometry_socket.attribute_domain = 'POINT'

    #Socket Geometry
    geometry_socket_1 = effector_passthrough.interface.new_socket(name = "Geometry", in_out='INPUT', socket_type = 'NodeSocketGeometry')
    geometry_socket_1.attribute_domain = 'POINT'


    #initialize effector_passthrough nodes
    #node Group Input
    group_input = effector_passthrough.nodes.new("NodeGroupInput")
    group_input.name = "Group Input"

    #node Group Output
    group_output = effector_passthrough.nodes.new("NodeGroupOutput")
    group_output.name = "Group Output"
    group_output.is_active_output = True





    #Set locations
    group_input.location = (-200.0, 0.0)
    group_output.location = (200.0, 0.0)

    #Set dimensions
    group_input.width, group_input.height = 140.0, 100.0
    group_output.width, group_output.height = 140.0, 100.0

    #initialize effector_passthrough links
    #group_input.Geometry -> group_output.Geometry
    effector_passthrough.links.new(group_input.outputs[0], group_output.inputs[0])
    return effector_passthrough

effector_passthrough = effector_passthrough_node_group()

#initialize collectioncloner_grid_collection node group
def collectioncloner_grid_collection_node_group():
    collectioncloner_grid_collection = bpy.data.node_groups.new(type = 'GeometryNodeTree', name = "CollectionCloner_GRID_Collection")

    collectioncloner_grid_collection.color_tag = 'NONE'
    collectioncloner_grid_collection.description = ""
    collectioncloner_grid_collection.default_group_node_width = 140
    


    #collectioncloner_grid_collection interface
    #Socket Geometry
    geometry_socket_2 = collectioncloner_grid_collection.interface.new_socket(name = "Geometry", in_out='OUTPUT', socket_type = 'NodeSocketGeometry')
    geometry_socket_2.attribute_domain = 'POINT'

    #Socket Count X
    count_x_socket = collectioncloner_grid_collection.interface.new_socket(name = "Count X", in_out='INPUT', socket_type = 'NodeSocketInt')
    count_x_socket.default_value = 3
    count_x_socket.min_value = 1
    count_x_socket.max_value = 100
    count_x_socket.subtype = 'NONE'
    count_x_socket.attribute_domain = 'POINT'

    #Socket Count Y
    count_y_socket = collectioncloner_grid_collection.interface.new_socket(name = "Count Y", in_out='INPUT', socket_type = 'NodeSocketInt')
    count_y_socket.default_value = 3
    count_y_socket.min_value = 1
    count_y_socket.max_value = 100
    count_y_socket.subtype = 'NONE'
    count_y_socket.attribute_domain = 'POINT'

    #Socket Count Z
    count_z_socket = collectioncloner_grid_collection.interface.new_socket(name = "Count Z", in_out='INPUT', socket_type = 'NodeSocketInt')
    count_z_socket.default_value = 1
    count_z_socket.min_value = 1
    count_z_socket.max_value = 100
    count_z_socket.subtype = 'NONE'
    count_z_socket.attribute_domain = 'POINT'

    #Socket Spacing
    spacing_socket = collectioncloner_grid_collection.interface.new_socket(name = "Spacing", in_out='INPUT', socket_type = 'NodeSocketVector')
    spacing_socket.default_value = (3.0, 3.0, 3.0)
    spacing_socket.min_value = -3.4028234663852886e+38
    spacing_socket.max_value = 3.4028234663852886e+38
    spacing_socket.subtype = 'NONE'
    spacing_socket.attribute_domain = 'POINT'

    #Socket Instance Rotation
    instance_rotation_socket = collectioncloner_grid_collection.interface.new_socket(name = "Instance Rotation", in_out='INPUT', socket_type = 'NodeSocketVector')
    instance_rotation_socket.default_value = (0.0, 0.0, 0.0)
    instance_rotation_socket.min_value = -3.4028234663852886e+38
    instance_rotation_socket.max_value = 3.4028234663852886e+38
    instance_rotation_socket.subtype = 'EULER'
    instance_rotation_socket.attribute_domain = 'POINT'

    #Socket Instance Scale
    instance_scale_socket = collectioncloner_grid_collection.interface.new_socket(name = "Instance Scale", in_out='INPUT', socket_type = 'NodeSocketVector')
    instance_scale_socket.default_value = (1.0, 1.0, 1.0)
    instance_scale_socket.min_value = -3.4028234663852886e+38
    instance_scale_socket.max_value = 3.4028234663852886e+38
    instance_scale_socket.subtype = 'NONE'
    instance_scale_socket.attribute_domain = 'POINT'

    #Socket Global Position
    global_position_socket = collectioncloner_grid_collection.interface.new_socket(name = "Global Position", in_out='INPUT', socket_type = 'NodeSocketVector')
    global_position_socket.default_value = (0.0, 0.0, 0.0)
    global_position_socket.min_value = -3.4028234663852886e+38
    global_position_socket.max_value = 3.4028234663852886e+38
    global_position_socket.subtype = 'NONE'
    global_position_socket.attribute_domain = 'POINT'

    #Socket Global Rotation
    global_rotation_socket = collectioncloner_grid_collection.interface.new_socket(name = "Global Rotation", in_out='INPUT', socket_type = 'NodeSocketVector')
    global_rotation_socket.default_value = (0.0, 0.0, 0.0)
    global_rotation_socket.min_value = -3.4028234663852886e+38
    global_rotation_socket.max_value = 3.4028234663852886e+38
    global_rotation_socket.subtype = 'EULER'
    global_rotation_socket.attribute_domain = 'POINT'

    #Socket Random Seed
    random_seed_socket = collectioncloner_grid_collection.interface.new_socket(name = "Random Seed", in_out='INPUT', socket_type = 'NodeSocketInt')
    random_seed_socket.default_value = 0
    random_seed_socket.min_value = 0
    random_seed_socket.max_value = 10000
    random_seed_socket.subtype = 'NONE'
    random_seed_socket.attribute_domain = 'POINT'

    #Socket Random Position
    random_position_socket = collectioncloner_grid_collection.interface.new_socket(name = "Random Position", in_out='INPUT', socket_type = 'NodeSocketVector')
    random_position_socket.default_value = (0.0, 0.0, 0.0)
    random_position_socket.min_value = -3.4028234663852886e+38
    random_position_socket.max_value = 3.4028234663852886e+38
    random_position_socket.subtype = 'NONE'
    random_position_socket.attribute_domain = 'POINT'

    #Socket Random Rotation
    random_rotation_socket = collectioncloner_grid_collection.interface.new_socket(name = "Random Rotation", in_out='INPUT', socket_type = 'NodeSocketVector')
    random_rotation_socket.default_value = (0.0, 0.0, 0.0)
    random_rotation_socket.min_value = -3.4028234663852886e+38
    random_rotation_socket.max_value = 3.4028234663852886e+38
    random_rotation_socket.subtype = 'EULER'
    random_rotation_socket.attribute_domain = 'POINT'

    #Socket Random Scale
    random_scale_socket = collectioncloner_grid_collection.interface.new_socket(name = "Random Scale", in_out='INPUT', socket_type = 'NodeSocketFloat')
    random_scale_socket.default_value = 0.0
    random_scale_socket.min_value = 0.0
    random_scale_socket.max_value = 1.0
    random_scale_socket.subtype = 'NONE'
    random_scale_socket.attribute_domain = 'POINT'

    #Socket Color
    color_socket = collectioncloner_grid_collection.interface.new_socket(name = "Color", in_out='INPUT', socket_type = 'NodeSocketColor')
    color_socket.default_value = (1.0, 1.0, 1.0, 1.0)
    color_socket.attribute_domain = 'POINT'

    #Socket Center Grid
    center_grid_socket = collectioncloner_grid_collection.interface.new_socket(name = "Center Grid", in_out='INPUT', socket_type = 'NodeSocketBool')
    center_grid_socket.default_value = True
    center_grid_socket.attribute_domain = 'POINT'

    #Socket Pick Random Instance
    pick_random_instance_socket = collectioncloner_grid_collection.interface.new_socket(name = "Pick Random Instance", in_out='INPUT', socket_type = 'NodeSocketBool')
    pick_random_instance_socket.default_value = False
    pick_random_instance_socket.attribute_domain = 'POINT'

    #Socket Use Effector
    use_effector_socket = collectioncloner_grid_collection.interface.new_socket(name = "Use Effector", in_out='INPUT', socket_type = 'NodeSocketBool')
    use_effector_socket.default_value = True
    use_effector_socket.attribute_domain = 'POINT'

    #Socket Realize Instances
    realize_instances_socket = collectioncloner_grid_collection.interface.new_socket(name = "Realize Instances", in_out='INPUT', socket_type = 'NodeSocketBool')
    realize_instances_socket.default_value = True
    realize_instances_socket.attribute_domain = 'POINT'

    #Socket Collection
    collection_socket = collectioncloner_grid_collection.interface.new_socket(name = "Collection", in_out='INPUT', socket_type = 'NodeSocketCollection')
    collection_socket.attribute_domain = 'POINT'


    #initialize collectioncloner_grid_collection nodes
    #node Group Input
    group_input_1 = collectioncloner_grid_collection.nodes.new("NodeGroupInput")
    group_input_1.name = "Group Input"

    #node Group Output
    group_output_1 = collectioncloner_grid_collection.nodes.new("NodeGroupOutput")
    group_output_1.name = "Group Output"
    group_output_1.is_active_output = True

    #node Collection Info
    collection_info = collectioncloner_grid_collection.nodes.new("GeometryNodeCollectionInfo")
    collection_info.name = "Collection Info"
    collection_info.transform_space = 'RELATIVE'
    #Separate Children
    collection_info.inputs[1].default_value = False
    #Reset Children
    collection_info.inputs[2].default_value = False

    #node Input Realize Instances
    input_realize_instances = collectioncloner_grid_collection.nodes.new("GeometryNodeRealizeInstances")
    input_realize_instances.name = "Input Realize Instances"
    #Selection
    input_realize_instances.inputs[1].default_value = True
    #Realize All
    input_realize_instances.inputs[2].default_value = True
    #Depth
    input_realize_instances.inputs[3].default_value = 0

    #node Input Realize Switch
    input_realize_switch = collectioncloner_grid_collection.nodes.new("GeometryNodeSwitch")
    input_realize_switch.name = "Input Realize Switch"
    input_realize_switch.input_type = 'GEOMETRY'

    #node Vector Math
    vector_math = collectioncloner_grid_collection.nodes.new("ShaderNodeVectorMath")
    vector_math.name = "Vector Math"
    vector_math.operation = 'MULTIPLY'
    #Vector_001
    vector_math.inputs[1].default_value = (1.0, 1.0, 1.0)

    #node Separate XYZ
    separate_xyz = collectioncloner_grid_collection.nodes.new("ShaderNodeSeparateXYZ")
    separate_xyz.name = "Separate XYZ"

    #node Line X Points
    line_x_points = collectioncloner_grid_collection.nodes.new("GeometryNodeMeshLine")
    line_x_points.name = "Line X Points"
    line_x_points.count_mode = 'TOTAL'
    line_x_points.mode = 'OFFSET'
    #Start Location
    line_x_points.inputs[2].default_value = (0.0, 0.0, 0.0)

    #node Combine XYZ
    combine_xyz = collectioncloner_grid_collection.nodes.new("ShaderNodeCombineXYZ")
    combine_xyz.name = "Combine XYZ"
    #Y
    combine_xyz.inputs[1].default_value = 0.0
    #Z
    combine_xyz.inputs[2].default_value = 0.0

    #node Line Y Points
    line_y_points = collectioncloner_grid_collection.nodes.new("GeometryNodeMeshLine")
    line_y_points.name = "Line Y Points"
    line_y_points.count_mode = 'TOTAL'
    line_y_points.mode = 'OFFSET'
    #Start Location
    line_y_points.inputs[2].default_value = (0.0, 0.0, 0.0)

    #node Combine XYZ.001
    combine_xyz_001 = collectioncloner_grid_collection.nodes.new("ShaderNodeCombineXYZ")
    combine_xyz_001.name = "Combine XYZ.001"
    #X
    combine_xyz_001.inputs[0].default_value = 0.0
    #Z
    combine_xyz_001.inputs[2].default_value = 0.0

    #node Mesh to Points
    mesh_to_points = collectioncloner_grid_collection.nodes.new("GeometryNodeMeshToPoints")
    mesh_to_points.name = "Mesh to Points"
    mesh_to_points.mode = 'VERTICES'
    #Selection
    mesh_to_points.inputs[1].default_value = True
    #Position
    mesh_to_points.inputs[2].default_value = (0.0, 0.0, 0.0)
    #Radius
    mesh_to_points.inputs[3].default_value = 0.05000000074505806

    #node Instance X on Y
    instance_x_on_y = collectioncloner_grid_collection.nodes.new("GeometryNodeInstanceOnPoints")
    instance_x_on_y.name = "Instance X on Y"
    #Selection
    instance_x_on_y.inputs[1].default_value = True
    #Pick Instance
    instance_x_on_y.inputs[3].default_value = False
    #Instance Index
    instance_x_on_y.inputs[4].default_value = 0
    #Rotation
    instance_x_on_y.inputs[5].default_value = (0.0, 0.0, 0.0)
    #Scale
    instance_x_on_y.inputs[6].default_value = (1.0, 1.0, 1.0)

    #node Realize 2D Grid
    realize_2d_grid = collectioncloner_grid_collection.nodes.new("GeometryNodeRealizeInstances")
    realize_2d_grid.name = "Realize 2D Grid"
    #Selection
    realize_2d_grid.inputs[1].default_value = True
    #Realize All
    realize_2d_grid.inputs[2].default_value = True
    #Depth
    realize_2d_grid.inputs[3].default_value = 0

    #node Line Z Points
    line_z_points = collectioncloner_grid_collection.nodes.new("GeometryNodeMeshLine")
    line_z_points.name = "Line Z Points"
    line_z_points.count_mode = 'TOTAL'
    line_z_points.mode = 'OFFSET'
    #Start Location
    line_z_points.inputs[2].default_value = (0.0, 0.0, 0.0)

    #node Combine XYZ.002
    combine_xyz_002 = collectioncloner_grid_collection.nodes.new("ShaderNodeCombineXYZ")
    combine_xyz_002.name = "Combine XYZ.002"
    #X
    combine_xyz_002.inputs[0].default_value = 0.0
    #Y
    combine_xyz_002.inputs[1].default_value = 0.0

    #node Instance 2D on Z
    instance_2d_on_z = collectioncloner_grid_collection.nodes.new("GeometryNodeInstanceOnPoints")
    instance_2d_on_z.name = "Instance 2D on Z"
    #Selection
    instance_2d_on_z.inputs[1].default_value = True
    #Pick Instance
    instance_2d_on_z.inputs[3].default_value = False
    #Instance Index
    instance_2d_on_z.inputs[4].default_value = 0
    #Rotation
    instance_2d_on_z.inputs[5].default_value = (0.0, 0.0, 0.0)
    #Scale
    instance_2d_on_z.inputs[6].default_value = (1.0, 1.0, 1.0)

    #node Realize 3D Grid
    realize_3d_grid = collectioncloner_grid_collection.nodes.new("GeometryNodeRealizeInstances")
    realize_3d_grid.name = "Realize 3D Grid"
    #Selection
    realize_3d_grid.inputs[1].default_value = True
    #Realize All
    realize_3d_grid.inputs[2].default_value = True
    #Depth
    realize_3d_grid.inputs[3].default_value = 0

    #node Compare
    compare = collectioncloner_grid_collection.nodes.new("FunctionNodeCompare")
    compare.name = "Compare"
    compare.data_type = 'INT'
    compare.mode = 'ELEMENT'
    compare.operation = 'GREATER_THAN'
    #B_INT
    compare.inputs[3].default_value = 1

    #node Switch 2D/3D Points
    switch_2d_3d_points = collectioncloner_grid_collection.nodes.new("GeometryNodeSwitch")
    switch_2d_3d_points.name = "Switch 2D/3D Points"
    switch_2d_3d_points.input_type = 'GEOMETRY'

    #node Mesh to Points.001
    mesh_to_points_001 = collectioncloner_grid_collection.nodes.new("GeometryNodeMeshToPoints")
    mesh_to_points_001.name = "Mesh to Points.001"
    mesh_to_points_001.mode = 'VERTICES'
    #Selection
    mesh_to_points_001.inputs[1].default_value = True
    #Position
    mesh_to_points_001.inputs[2].default_value = (0.0, 0.0, 0.0)
    #Radius
    mesh_to_points_001.inputs[3].default_value = 0.05000000074505806

    #node Index
    index = collectioncloner_grid_collection.nodes.new("GeometryNodeInputIndex")
    index.name = "Index"

    #node Random Value
    random_value = collectioncloner_grid_collection.nodes.new("FunctionNodeRandomValue")
    random_value.name = "Random Value"
    random_value.data_type = 'FLOAT_VECTOR'

    #node Vector Math.001
    vector_math_001 = collectioncloner_grid_collection.nodes.new("ShaderNodeVectorMath")
    vector_math_001.name = "Vector Math.001"
    vector_math_001.operation = 'MULTIPLY'
    #Vector_001
    vector_math_001.inputs[1].default_value = (-1.0, -1.0, -1.0)

    #node Random Value.001
    random_value_001 = collectioncloner_grid_collection.nodes.new("FunctionNodeRandomValue")
    random_value_001.name = "Random Value.001"
    random_value_001.data_type = 'FLOAT_VECTOR'

    #node Vector Math.002
    vector_math_002 = collectioncloner_grid_collection.nodes.new("ShaderNodeVectorMath")
    vector_math_002.name = "Vector Math.002"
    vector_math_002.operation = 'MULTIPLY'
    #Vector_001
    vector_math_002.inputs[1].default_value = (-1.0, -1.0, -1.0)

    #node Random Value.002
    random_value_002 = collectioncloner_grid_collection.nodes.new("FunctionNodeRandomValue")
    random_value_002.name = "Random Value.002"
    random_value_002.data_type = 'FLOAT'

    #node Math
    math = collectioncloner_grid_collection.nodes.new("ShaderNodeMath")
    math.name = "Math"
    math.operation = 'MULTIPLY'
    math.use_clamp = False
    #Value_001
    math.inputs[1].default_value = -1.0

    #node Instance on Points
    instance_on_points = collectioncloner_grid_collection.nodes.new("GeometryNodeInstanceOnPoints")
    instance_on_points.name = "Instance on Points"
    #Selection
    instance_on_points.inputs[1].default_value = True
    #Pick Instance
    instance_on_points.inputs[3].default_value = False
    #Instance Index
    instance_on_points.inputs[4].default_value = 0
    #Rotation
    instance_on_points.inputs[5].default_value = (0.0, 0.0, 0.0)
    #Scale
    instance_on_points.inputs[6].default_value = (1.0, 1.0, 1.0)

    #node Translate Instances
    translate_instances = collectioncloner_grid_collection.nodes.new("GeometryNodeTranslateInstances")
    translate_instances.name = "Translate Instances"
    #Selection
    translate_instances.inputs[1].default_value = True
    #Local Space
    translate_instances.inputs[3].default_value = True

    #node Vector Math.003
    vector_math_003 = collectioncloner_grid_collection.nodes.new("ShaderNodeVectorMath")
    vector_math_003.name = "Vector Math.003"
    vector_math_003.operation = 'ADD'

    #node Rotate Instances
    rotate_instances = collectioncloner_grid_collection.nodes.new("GeometryNodeRotateInstances")
    rotate_instances.name = "Rotate Instances"
    #Selection
    rotate_instances.inputs[1].default_value = True
    #Pivot Point
    rotate_instances.inputs[3].default_value = (0.0, 0.0, 0.0)
    #Local Space
    rotate_instances.inputs[4].default_value = True

    #node Combine XYZ.003
    combine_xyz_003 = collectioncloner_grid_collection.nodes.new("ShaderNodeCombineXYZ")
    combine_xyz_003.name = "Combine XYZ.003"

    #node Vector Math.004
    vector_math_004 = collectioncloner_grid_collection.nodes.new("ShaderNodeVectorMath")
    vector_math_004.name = "Vector Math.004"
    vector_math_004.operation = 'ADD'

    #node Scale Instances
    scale_instances = collectioncloner_grid_collection.nodes.new("GeometryNodeScaleInstances")
    scale_instances.name = "Scale Instances"
    #Selection
    scale_instances.inputs[1].default_value = True
    #Center
    scale_instances.inputs[3].default_value = (0.0, 0.0, 0.0)
    #Local Space
    scale_instances.inputs[4].default_value = True

    #node Transform Geometry
    transform_geometry = collectioncloner_grid_collection.nodes.new("GeometryNodeTransform")
    transform_geometry.name = "Transform Geometry"
    transform_geometry.mode = 'COMPONENTS'
    #Scale
    transform_geometry.inputs[3].default_value = (1.0, 1.0, 1.0)

    #node Effector_Random_Effector
    effector_random_effector = collectioncloner_grid_collection.nodes.new("GeometryNodeGroup")
    effector_random_effector.name = "Effector_Random_Effector"
    effector_random_effector.node_tree = effector_passthrough

    #node Final Realize Instances
    final_realize_instances = collectioncloner_grid_collection.nodes.new("GeometryNodeRealizeInstances")
    final_realize_instances.name = "Final Realize Instances"
    #Selection
    final_realize_instances.inputs[1].default_value = True
    #Realize All
    final_realize_instances.inputs[2].default_value = True
    #Depth
    final_realize_instances.inputs[3].default_value = 0

    #node Final Realize Switch
    final_realize_switch = collectioncloner_grid_collection.nodes.new("GeometryNodeSwitch")
    final_realize_switch.name = "Final Realize Switch"
    final_realize_switch.input_type = 'GEOMETRY'





    #Set locations
    group_input_1.location = (-1000.0, 0.0)
    group_output_1.location = (1000.0, 0.0)
    collection_info.location = (-800.0, -200.0)
    input_realize_instances.location = (-320.3174743652344, -420.94134521484375)
    input_realize_switch.location = (-600.0, -200.0)
    vector_math.location = (-900.0, 100.0)
    separate_xyz.location = (-750.0, 100.0)
    line_x_points.location = (-700.0, 300.0)
    combine_xyz.location = (-800.0, 300.0)
    line_y_points.location = (-700.0, 200.0)
    combine_xyz_001.location = (-800.0, 200.0)
    mesh_to_points.location = (-600.0, 300.0)
    instance_x_on_y.location = (-500.0, 250.0)
    realize_2d_grid.location = (-400.0, 250.0)
    line_z_points.location = (-700.0, 100.0)
    combine_xyz_002.location = (-800.0, 100.0)
    instance_2d_on_z.location = (-300.0, 200.0)
    realize_3d_grid.location = (-200.0, 200.0)
    compare.location = (-300.0, 100.0)
    switch_2d_3d_points.location = (-100.0, 150.0)
    mesh_to_points_001.location = (300.0, 100.0)
    index.location = (350.0, -100.0)
    random_value.location = (400.0, -150.0)
    vector_math_001.location = (300.0, -150.0)
    random_value_001.location = (400.0, -250.0)
    vector_math_002.location = (300.0, -250.0)
    random_value_002.location = (400.0, -350.0)
    math.location = (300.0, -350.0)
    instance_on_points.location = (400.0, 100.0)
    translate_instances.location = (500.0, 100.0)
    vector_math_003.location = (500.0, 0.0)
    rotate_instances.location = (600.0, 100.0)
    combine_xyz_003.location = (600.0, -100.0)
    vector_math_004.location = (600.0, -50.0)
    scale_instances.location = (700.0, 100.0)
    transform_geometry.location = (597.6541748046875, 311.830322265625)
    effector_random_effector.location = (1231.93798828125, 295.8861083984375)
    final_realize_instances.location = (1000.0, 0.0)
    final_realize_switch.location = (1625.2076416015625, 129.83151245117188)

    #Set dimensions
    group_input_1.width, group_input_1.height = 140.0, 100.0
    group_output_1.width, group_output_1.height = 140.0, 100.0
    collection_info.width, collection_info.height = 140.0, 100.0
    input_realize_instances.width, input_realize_instances.height = 140.0, 100.0
    input_realize_switch.width, input_realize_switch.height = 140.0, 100.0
    vector_math.width, vector_math.height = 140.0, 100.0
    separate_xyz.width, separate_xyz.height = 140.0, 100.0
    line_x_points.width, line_x_points.height = 140.0, 100.0
    combine_xyz.width, combine_xyz.height = 140.0, 100.0
    line_y_points.width, line_y_points.height = 140.0, 100.0
    combine_xyz_001.width, combine_xyz_001.height = 140.0, 100.0
    mesh_to_points.width, mesh_to_points.height = 140.0, 100.0
    instance_x_on_y.width, instance_x_on_y.height = 140.0, 100.0
    realize_2d_grid.width, realize_2d_grid.height = 140.0, 100.0
    line_z_points.width, line_z_points.height = 140.0, 100.0
    combine_xyz_002.width, combine_xyz_002.height = 140.0, 100.0
    instance_2d_on_z.width, instance_2d_on_z.height = 140.0, 100.0
    realize_3d_grid.width, realize_3d_grid.height = 140.0, 100.0
    compare.width, compare.height = 140.0, 100.0
    switch_2d_3d_points.width, switch_2d_3d_points.height = 140.0, 100.0
    mesh_to_points_001.width, mesh_to_points_001.height = 140.0, 100.0
    index.width, index.height = 140.0, 100.0
    random_value.width, random_value.height = 140.0, 100.0
    vector_math_001.width, vector_math_001.height = 140.0, 100.0
    random_value_001.width, random_value_001.height = 140.0, 100.0
    vector_math_002.width, vector_math_002.height = 140.0, 100.0
    random_value_002.width, random_value_002.height = 140.0, 100.0
    math.width, math.height = 140.0, 100.0
    instance_on_points.width, instance_on_points.height = 140.0, 100.0
    translate_instances.width, translate_instances.height = 140.0, 100.0
    vector_math_003.width, vector_math_003.height = 140.0, 100.0
    rotate_instances.width, rotate_instances.height = 140.0, 100.0
    combine_xyz_003.width, combine_xyz_003.height = 140.0, 100.0
    vector_math_004.width, vector_math_004.height = 140.0, 100.0
    scale_instances.width, scale_instances.height = 140.0, 100.0
    transform_geometry.width, transform_geometry.height = 140.0, 100.0
    effector_random_effector.width, effector_random_effector.height = 140.0, 100.0
    final_realize_instances.width, final_realize_instances.height = 140.0, 100.0
    final_realize_switch.width, final_realize_switch.height = 140.0, 100.0

    #initialize collectioncloner_grid_collection links
    #group_input_1.Collection -> collection_info.Collection
    collectioncloner_grid_collection.links.new(group_input_1.outputs[17], collection_info.inputs[0])
    #collection_info.Instances -> input_realize_switch.Switch
    collectioncloner_grid_collection.links.new(collection_info.outputs[0], input_realize_switch.inputs[0])
    #input_realize_instances.Geometry -> input_realize_switch.False
    collectioncloner_grid_collection.links.new(input_realize_instances.outputs[0], input_realize_switch.inputs[1])
    #group_input_1.Spacing -> vector_math.Vector
    collectioncloner_grid_collection.links.new(group_input_1.outputs[3], vector_math.inputs[0])
    #vector_math.Vector -> separate_xyz.Vector
    collectioncloner_grid_collection.links.new(vector_math.outputs[0], separate_xyz.inputs[0])
    #group_input_1.Count X -> line_x_points.Count
    collectioncloner_grid_collection.links.new(group_input_1.outputs[0], line_x_points.inputs[0])
    #separate_xyz.X -> combine_xyz.X
    collectioncloner_grid_collection.links.new(separate_xyz.outputs[0], combine_xyz.inputs[0])
    #combine_xyz.Vector -> line_x_points.Offset
    collectioncloner_grid_collection.links.new(combine_xyz.outputs[0], line_x_points.inputs[3])
    #group_input_1.Count Y -> line_y_points.Count
    collectioncloner_grid_collection.links.new(group_input_1.outputs[1], line_y_points.inputs[0])
    #separate_xyz.Y -> combine_xyz_001.Y
    collectioncloner_grid_collection.links.new(separate_xyz.outputs[1], combine_xyz_001.inputs[1])
    #combine_xyz_001.Vector -> line_y_points.Offset
    collectioncloner_grid_collection.links.new(combine_xyz_001.outputs[0], line_y_points.inputs[3])
    #line_x_points.Mesh -> mesh_to_points.Mesh
    collectioncloner_grid_collection.links.new(line_x_points.outputs[0], mesh_to_points.inputs[0])
    #line_y_points.Mesh -> instance_x_on_y.Points
    collectioncloner_grid_collection.links.new(line_y_points.outputs[0], instance_x_on_y.inputs[0])
    #line_x_points.Mesh -> instance_x_on_y.Instance
    collectioncloner_grid_collection.links.new(line_x_points.outputs[0], instance_x_on_y.inputs[2])
    #instance_x_on_y.Instances -> realize_2d_grid.Geometry
    collectioncloner_grid_collection.links.new(instance_x_on_y.outputs[0], realize_2d_grid.inputs[0])
    #group_input_1.Count Z -> line_z_points.Count
    collectioncloner_grid_collection.links.new(group_input_1.outputs[2], line_z_points.inputs[0])
    #separate_xyz.Z -> combine_xyz_002.Z
    collectioncloner_grid_collection.links.new(separate_xyz.outputs[2], combine_xyz_002.inputs[2])
    #combine_xyz_002.Vector -> line_z_points.Offset
    collectioncloner_grid_collection.links.new(combine_xyz_002.outputs[0], line_z_points.inputs[3])
    #line_z_points.Mesh -> instance_2d_on_z.Points
    collectioncloner_grid_collection.links.new(line_z_points.outputs[0], instance_2d_on_z.inputs[0])
    #realize_2d_grid.Geometry -> instance_2d_on_z.Instance
    collectioncloner_grid_collection.links.new(realize_2d_grid.outputs[0], instance_2d_on_z.inputs[2])
    #instance_2d_on_z.Instances -> realize_3d_grid.Geometry
    collectioncloner_grid_collection.links.new(instance_2d_on_z.outputs[0], realize_3d_grid.inputs[0])
    #group_input_1.Count Z -> compare.A
    collectioncloner_grid_collection.links.new(group_input_1.outputs[2], compare.inputs[2])
    #compare.Result -> switch_2d_3d_points.Switch
    collectioncloner_grid_collection.links.new(compare.outputs[0], switch_2d_3d_points.inputs[0])
    #realize_2d_grid.Geometry -> switch_2d_3d_points.False
    collectioncloner_grid_collection.links.new(realize_2d_grid.outputs[0], switch_2d_3d_points.inputs[1])
    #realize_3d_grid.Geometry -> switch_2d_3d_points.True
    collectioncloner_grid_collection.links.new(realize_3d_grid.outputs[0], switch_2d_3d_points.inputs[2])
    #switch_2d_3d_points.Output -> mesh_to_points_001.Mesh
    collectioncloner_grid_collection.links.new(switch_2d_3d_points.outputs[0], mesh_to_points_001.inputs[0])
    #group_input_1.Random Position -> vector_math_001.Vector
    collectioncloner_grid_collection.links.new(group_input_1.outputs[9], vector_math_001.inputs[0])
    #group_input_1.Random Seed -> random_value.Seed
    collectioncloner_grid_collection.links.new(group_input_1.outputs[8], random_value.inputs[8])
    #index.Index -> random_value.ID
    collectioncloner_grid_collection.links.new(index.outputs[0], random_value.inputs[7])
    #vector_math_001.Vector -> random_value.Min
    collectioncloner_grid_collection.links.new(vector_math_001.outputs[0], random_value.inputs[0])
    #group_input_1.Random Position -> random_value.Max
    collectioncloner_grid_collection.links.new(group_input_1.outputs[9], random_value.inputs[1])
    #group_input_1.Random Rotation -> vector_math_002.Vector
    collectioncloner_grid_collection.links.new(group_input_1.outputs[10], vector_math_002.inputs[0])
    #group_input_1.Random Seed -> random_value_001.Seed
    collectioncloner_grid_collection.links.new(group_input_1.outputs[8], random_value_001.inputs[8])
    #index.Index -> random_value_001.ID
    collectioncloner_grid_collection.links.new(index.outputs[0], random_value_001.inputs[7])
    #vector_math_002.Vector -> random_value_001.Min
    collectioncloner_grid_collection.links.new(vector_math_002.outputs[0], random_value_001.inputs[0])
    #group_input_1.Random Rotation -> random_value_001.Max
    collectioncloner_grid_collection.links.new(group_input_1.outputs[10], random_value_001.inputs[1])
    #group_input_1.Random Scale -> math.Value
    collectioncloner_grid_collection.links.new(group_input_1.outputs[11], math.inputs[0])
    #group_input_1.Random Seed -> random_value_002.Seed
    collectioncloner_grid_collection.links.new(group_input_1.outputs[8], random_value_002.inputs[8])
    #index.Index -> random_value_002.ID
    collectioncloner_grid_collection.links.new(index.outputs[0], random_value_002.inputs[7])
    #math.Value -> random_value_002.Min
    collectioncloner_grid_collection.links.new(math.outputs[0], random_value_002.inputs[2])
    #group_input_1.Random Scale -> random_value_002.Max
    collectioncloner_grid_collection.links.new(group_input_1.outputs[11], random_value_002.inputs[3])
    #mesh_to_points_001.Points -> instance_on_points.Points
    collectioncloner_grid_collection.links.new(mesh_to_points_001.outputs[0], instance_on_points.inputs[0])
    #input_realize_switch.Output -> instance_on_points.Instance
    collectioncloner_grid_collection.links.new(input_realize_switch.outputs[0], instance_on_points.inputs[2])
    #instance_on_points.Instances -> translate_instances.Instances
    collectioncloner_grid_collection.links.new(instance_on_points.outputs[0], translate_instances.inputs[0])
    #random_value.Value -> translate_instances.Translation
    collectioncloner_grid_collection.links.new(random_value.outputs[0], translate_instances.inputs[2])
    #group_input_1.Instance Rotation -> vector_math_003.Vector
    collectioncloner_grid_collection.links.new(group_input_1.outputs[4], vector_math_003.inputs[0])
    #random_value_001.Value -> vector_math_003.Vector
    collectioncloner_grid_collection.links.new(random_value_001.outputs[0], vector_math_003.inputs[1])
    #translate_instances.Instances -> rotate_instances.Instances
    collectioncloner_grid_collection.links.new(translate_instances.outputs[0], rotate_instances.inputs[0])
    #vector_math_003.Vector -> rotate_instances.Rotation
    collectioncloner_grid_collection.links.new(vector_math_003.outputs[0], rotate_instances.inputs[2])
    #random_value_002.Value -> combine_xyz_003.X
    collectioncloner_grid_collection.links.new(random_value_002.outputs[1], combine_xyz_003.inputs[0])
    #random_value_002.Value -> combine_xyz_003.Y
    collectioncloner_grid_collection.links.new(random_value_002.outputs[1], combine_xyz_003.inputs[1])
    #random_value_002.Value -> combine_xyz_003.Z
    collectioncloner_grid_collection.links.new(random_value_002.outputs[1], combine_xyz_003.inputs[2])
    #group_input_1.Instance Scale -> vector_math_004.Vector
    collectioncloner_grid_collection.links.new(group_input_1.outputs[5], vector_math_004.inputs[0])
    #combine_xyz_003.Vector -> vector_math_004.Vector
    collectioncloner_grid_collection.links.new(combine_xyz_003.outputs[0], vector_math_004.inputs[1])
    #rotate_instances.Instances -> scale_instances.Instances
    collectioncloner_grid_collection.links.new(rotate_instances.outputs[0], scale_instances.inputs[0])
    #vector_math_004.Vector -> scale_instances.Scale
    collectioncloner_grid_collection.links.new(vector_math_004.outputs[0], scale_instances.inputs[2])
    #scale_instances.Instances -> transform_geometry.Geometry
    collectioncloner_grid_collection.links.new(scale_instances.outputs[0], transform_geometry.inputs[0])
    #group_input_1.Global Position -> transform_geometry.Translation
    collectioncloner_grid_collection.links.new(group_input_1.outputs[6], transform_geometry.inputs[1])
    #group_input_1.Global Rotation -> transform_geometry.Rotation
    collectioncloner_grid_collection.links.new(group_input_1.outputs[7], transform_geometry.inputs[2])
    #transform_geometry.Geometry -> final_realize_instances.Geometry
    collectioncloner_grid_collection.links.new(transform_geometry.outputs[0], final_realize_instances.inputs[0])
    #effector_random_effector.Geometry -> final_realize_switch.False
    collectioncloner_grid_collection.links.new(effector_random_effector.outputs[0], final_realize_switch.inputs[1])
    #group_input_1.Realize Instances -> final_realize_switch.Switch
    collectioncloner_grid_collection.links.new(group_input_1.outputs[16], final_realize_switch.inputs[0])
    #final_realize_switch.Output -> group_output_1.Geometry
    collectioncloner_grid_collection.links.new(final_realize_switch.outputs[0], group_output_1.inputs[0])
    #transform_geometry.Geometry -> effector_random_effector.Geometry
    collectioncloner_grid_collection.links.new(transform_geometry.outputs[0], effector_random_effector.inputs[0])
    #transform_geometry.Geometry -> input_realize_instances.Geometry
    collectioncloner_grid_collection.links.new(transform_geometry.outputs[0], input_realize_instances.inputs[0])
    return collectioncloner_grid_collection

collectioncloner_grid_collection = collectioncloner_grid_collection_node_group()

