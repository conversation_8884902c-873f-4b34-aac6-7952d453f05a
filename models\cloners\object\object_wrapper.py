"""
Object Cloner Wrapper Module

Создает двухуровневую архитектуру для объектных клонеров:
1. Внутренняя группа - чистая логика клонирования
2. Внешняя группа - внутренняя группа + эффектор + финальные узлы
"""

import bpy


def create_object_cloner_core_group(cloner_type, orig_obj, name_suffix="", use_anti_recursion=False):
    """
    Создает ВНУТРЕННЮЮ группу с чистой логикой клонирования объектов (без эффекторов).
    
    Args:
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        orig_obj: Исходный объект для клонирования
        name_suffix: Суффикс для имени группы
        use_anti_recursion: Использовать анти-рекурсию
        
    Returns:
        Внутренняя группа с четко определенными выходами
    """
    # Создаем имя для внутренней группы
    core_group_name = f"ObjectCloner_{cloner_type}_Core{name_suffix}"
    counter = 1
    while core_group_name in bpy.data.node_groups:
        core_group_name = f"ObjectCloner_{cloner_type}_Core_{counter:03d}{name_suffix}"
        counter += 1

    # Создаем внутреннюю группу
    core_group = bpy.data.node_groups.new(core_group_name, 'GeometryNodeTree')
    
    # Создаем интерфейс для внутренней группы
    _create_core_interface(core_group, cloner_type, orig_obj)
    
    # Создаем узлы внутренней группы
    _create_core_nodes(core_group, cloner_type, orig_obj, use_anti_recursion)
    
    # Добавляем метаданные
    core_group["is_object_cloner_core"] = True
    core_group["cloner_type"] = cloner_type
    core_group["target_object"] = orig_obj.name
    
    print(f"[DEBUG] Создана внутренняя группа объектного клонера: {core_group.name}")
    return core_group


def create_object_cloner_wrapper_group(cloner_type, orig_obj, name_suffix="", use_anti_recursion=False):
    """
    Создает ВНЕШНЮЮ группу-обертку с эффектором для объектных клонеров.
    
    Args:
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        orig_obj: Исходный объект для клонирования
        name_suffix: Суффикс для имени группы
        use_anti_recursion: Использовать анти-рекурсию
        
    Returns:
        Внешняя группа с внутренней группой + эффектором
    """
    # Создаем внутреннюю группу
    core_group = create_object_cloner_core_group(
        cloner_type, orig_obj, name_suffix, use_anti_recursion
    )
    
    if not core_group:
        return None
    
    # Создаем внешнюю группу-обертку
    wrapper_group_name = f"ObjectCloner_{cloner_type}{name_suffix}"
    counter = 1
    while wrapper_group_name in bpy.data.node_groups:
        wrapper_group_name = f"ObjectCloner_{cloner_type}_{counter:03d}{name_suffix}"
        counter += 1

    wrapper_group = bpy.data.node_groups.new(wrapper_group_name, 'GeometryNodeTree')
    
    # Копируем интерфейс из внутренней группы
    _copy_interface(core_group, wrapper_group)
    
    # Создаем узлы во внешней группе
    _create_wrapper_nodes(wrapper_group, core_group, use_anti_recursion)
    
    # Добавляем метаданные
    wrapper_group["is_object_cloner"] = True
    wrapper_group["cloner_type"] = cloner_type
    wrapper_group["core_group"] = core_group.name
    wrapper_group["target_object"] = orig_obj.name
    wrapper_group["linked_effectors"] = []
    
    print(f"[DEBUG] Создана внешняя группа-обертка объектного клонера: {wrapper_group.name}")
    return wrapper_group


def _create_core_interface(core_group, cloner_type, orig_obj):
    """Создает интерфейс для внутренней группы объектного клонера"""
    # Выходной сокет
    core_group.interface.new_socket("Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')
    
    # Входные сокеты в зависимости от типа клонера
    if cloner_type == "GRID":
        # Grid клонер
        count_x = core_group.interface.new_socket("Count X", in_out='INPUT', socket_type='NodeSocketInt')
        count_x.default_value = 3
        
        count_y = core_group.interface.new_socket("Count Y", in_out='INPUT', socket_type='NodeSocketInt')
        count_y.default_value = 3
        
        count_z = core_group.interface.new_socket("Count Z", in_out='INPUT', socket_type='NodeSocketInt')
        count_z.default_value = 1
        
        spacing = core_group.interface.new_socket("Spacing", in_out='INPUT', socket_type='NodeSocketVector')
        spacing.default_value = (3.0, 3.0, 3.0)
        
    elif cloner_type == "LINEAR":
        # Linear клонер
        count = core_group.interface.new_socket("Count", in_out='INPUT', socket_type='NodeSocketInt')
        count.default_value = 5
        
        offset = core_group.interface.new_socket("Offset", in_out='INPUT', socket_type='NodeSocketVector')
        offset.default_value = (3.0, 0.0, 0.0)
        
    elif cloner_type == "CIRCLE":
        # Circle клонер
        count = core_group.interface.new_socket("Count", in_out='INPUT', socket_type='NodeSocketInt')
        count.default_value = 8
        
        radius = core_group.interface.new_socket("Radius", in_out='INPUT', socket_type='NodeSocketFloat')
        radius.default_value = 5.0
    
    # Общие параметры для всех типов
    # Трансформации инстансов
    instance_rotation = core_group.interface.new_socket("Instance Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    instance_rotation.default_value = (0.0, 0.0, 0.0)
    
    instance_scale = core_group.interface.new_socket("Instance Scale", in_out='INPUT', socket_type='NodeSocketVector')
    instance_scale.default_value = (1.0, 1.0, 1.0)
    
    # Рандомизация
    random_position = core_group.interface.new_socket("Random Position", in_out='INPUT', socket_type='NodeSocketVector')
    random_position.default_value = (0.0, 0.0, 0.0)
    
    random_rotation = core_group.interface.new_socket("Random Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    random_rotation.default_value = (0.0, 0.0, 0.0)
    
    random_scale = core_group.interface.new_socket("Random Scale", in_out='INPUT', socket_type='NodeSocketFloat')
    random_scale.default_value = 0.0
    
    random_seed = core_group.interface.new_socket("Random Seed", in_out='INPUT', socket_type='NodeSocketInt')
    random_seed.default_value = 0
    
    # Глобальные трансформации
    global_position = core_group.interface.new_socket("Global Position", in_out='INPUT', socket_type='NodeSocketVector')
    global_position.default_value = (0.0, 0.0, 0.0)
    
    global_rotation = core_group.interface.new_socket("Global Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    global_rotation.default_value = (0.0, 0.0, 0.0)
    
    # Анти-рекурсия
    realize_instances = core_group.interface.new_socket("Realize Instances", in_out='INPUT', socket_type='NodeSocketBool')
    realize_instances.default_value = False


def _create_core_nodes(core_group, cloner_type, orig_obj, use_anti_recursion):
    """Создает узлы для внутренней группы объектного клонера"""
    nodes = core_group.nodes
    links = core_group.links
    
    # Входной и выходной узлы
    group_input = nodes.new('NodeGroupInput')
    group_input.location = (-800, 0)
    
    group_output = nodes.new('NodeGroupOutput')
    group_output.location = (800, 0)
    
    # Object Info узел для получения геометрии объекта
    object_info = nodes.new('GeometryNodeObjectInfo')
    object_info.location = (-600, 0)
    object_info.inputs['Object'].default_value = orig_obj
    
    # Используем существующую логику из соответствующих клонеров
    if cloner_type == "GRID":
        final_output = _create_grid_logic(nodes, links, group_input, object_info)
    elif cloner_type == "LINEAR":
        final_output = _create_linear_logic(nodes, links, group_input, object_info)
    elif cloner_type == "CIRCLE":
        final_output = _create_circle_logic(nodes, links, group_input, object_info)
    else:
        # Fallback - прямое подключение
        final_output = object_info.outputs['Geometry']
    
    # Подключаем к выходу
    links.new(final_output, group_output.inputs['Geometry'])


def _create_grid_logic(nodes, links, group_input, object_info):
    """Создает логику Grid клонера (упрощенная версия)"""
    # Это упрощенная версия - в реальности нужно использовать полную логику из GridCloner
    # Пока создаем базовую структуру
    
    # Instance on Points для создания сетки
    instance_on_points = nodes.new('GeometryNodeInstanceOnPoints')
    instance_on_points.location = (0, 0)
    
    # Создаем простую сетку точек (упрощенно)
    grid_points = nodes.new('GeometryNodeMeshGrid')
    grid_points.location = (-200, 100)
    
    # Подключения
    links.new(grid_points.outputs['Mesh'], instance_on_points.inputs['Points'])
    links.new(object_info.outputs['Geometry'], instance_on_points.inputs['Instance'])
    
    return instance_on_points.outputs['Instances']


def _create_linear_logic(nodes, links, group_input, object_info):
    """Создает логику Linear клонера (упрощенная версия)"""
    # Упрощенная версия Linear клонера
    instance_on_points = nodes.new('GeometryNodeInstanceOnPoints')
    instance_on_points.location = (0, 0)
    
    # Создаем линию точек
    line_points = nodes.new('GeometryNodeMeshLine')
    line_points.location = (-200, 100)
    
    # Подключения
    links.new(line_points.outputs['Mesh'], instance_on_points.inputs['Points'])
    links.new(object_info.outputs['Geometry'], instance_on_points.inputs['Instance'])
    
    return instance_on_points.outputs['Instances']


def _create_circle_logic(nodes, links, group_input, object_info):
    """Создает логику Circle клонера (упрощенная версия)"""
    # Упрощенная версия Circle клонера
    instance_on_points = nodes.new('GeometryNodeInstanceOnPoints')
    instance_on_points.location = (0, 0)
    
    # Создаем круг точек
    circle_points = nodes.new('GeometryNodeMeshCircle')
    circle_points.location = (-200, 100)
    
    # Подключения
    links.new(circle_points.outputs['Mesh'], instance_on_points.inputs['Points'])
    links.new(object_info.outputs['Geometry'], instance_on_points.inputs['Instance'])
    
    return instance_on_points.outputs['Instances']


def _copy_interface(source_group, target_group):
    """Копирует интерфейс из исходной группы в целевую"""
    try:
        # Копируем входные сокеты
        for input_socket in source_group.interface.items_tree:
            if input_socket.in_out == 'INPUT':
                target_group.interface.new_socket(
                    input_socket.name,
                    in_out='INPUT',
                    socket_type=input_socket.socket_type
                )
        
        # Копируем выходные сокеты
        for output_socket in source_group.interface.items_tree:
            if output_socket.in_out == 'OUTPUT':
                target_group.interface.new_socket(
                    output_socket.name,
                    in_out='OUTPUT',
                    socket_type=output_socket.socket_type
                )
                
        print(f"[DEBUG] Интерфейс скопирован из {source_group.name} в {target_group.name}")
        
    except Exception as e:
        print(f"[ERROR] Ошибка при копировании интерфейса: {e}")


def _create_wrapper_nodes(wrapper_group, core_group, use_anti_recursion):
    """Создает узлы во внешней группе-обертке для объектных клонеров"""
    nodes = wrapper_group.nodes
    links = wrapper_group.links
    
    # Входной и выходной узлы
    group_input = nodes.new('NodeGroupInput')
    group_input.location = (-800, 0)
    
    group_output = nodes.new('NodeGroupOutput')
    group_output.location = (800, 0)
    
    # Узел внутренней группы клонера
    core_cloner_node = nodes.new('GeometryNodeGroup')
    core_cloner_node.node_tree = core_group
    core_cloner_node.name = "Core Cloner"
    core_cloner_node.location = (-400, 0)
    
    # Подключаем все входы от group_input к core_cloner_node
    for i, input_socket in enumerate(group_input.outputs):
        if i < len(core_cloner_node.inputs):
            links.new(input_socket, core_cloner_node.inputs[i])
    
    if use_anti_recursion:
        # Создаем placeholder для эффектора
        effector_placeholder = nodes.new('GeometryNodeGroup')
        effector_placeholder.name = "Effector_Random_Effector"
        effector_placeholder.location = (0, 0)
        
        # Создаем простую passthrough группу для placeholder
        _create_effector_placeholder(effector_placeholder)
        
        # Финальный узел Realize Instances
        final_realize = nodes.new('GeometryNodeRealizeInstances')
        final_realize.name = "Final Realize Instances"
        final_realize.location = (200, 100)
        final_realize.inputs[1].default_value = True  # Selection
        final_realize.inputs[2].default_value = True  # Realize All
        final_realize.inputs[3].default_value = 0     # Depth
        
        # Финальный переключатель
        final_switch = nodes.new('GeometryNodeSwitch')
        final_switch.input_type = 'GEOMETRY'
        final_switch.name = "Final Realize Switch"
        final_switch.location = (400, 0)
        
        # ПРАВИЛЬНЫЕ СВЯЗИ согласно AFTER.py:
        # 1. Core Cloner -> Effector
        links.new(core_cloner_node.outputs['Geometry'], effector_placeholder.inputs[0])
        
        # 2. Core Cloner -> Final Realize Instances (параллельно)
        links.new(core_cloner_node.outputs['Geometry'], final_realize.inputs['Geometry'])
        
        # 3. Effector -> Final Switch.False
        links.new(effector_placeholder.outputs[0], final_switch.inputs['False'])
        
        # 4. Final Realize -> Final Switch.True (для анти-рекурсии)
        links.new(final_realize.outputs['Geometry'], final_switch.inputs['True'])
        
        # 5. Group Input.Realize Instances -> Final Switch.Switch
        realize_input = None
        for output in group_input.outputs:
            if "realize" in output.name.lower() and "instances" in output.name.lower():
                realize_input = output
                break
        
        if realize_input:
            links.new(realize_input, final_switch.inputs['Switch'])
        
        # 6. Final Switch -> Group Output
        links.new(final_switch.outputs['Output'], group_output.inputs['Geometry'])
        
        print(f"[DEBUG] Создана архитектура объектного клонера с эффектором и анти-рекурсией")
        
    else:
        # Без анти-рекурсии - прямое подключение
        links.new(core_cloner_node.outputs['Geometry'], group_output.inputs['Geometry'])
        print(f"[DEBUG] Создана простая архитектура объектного клонера без анти-рекурсии")


def _create_effector_placeholder(effector_node):
    """Создает простую passthrough группу для placeholder эффектора"""
    try:
        passthrough_group = bpy.data.node_groups.new("Effector_Passthrough_Object", 'GeometryNodeTree')
        
        # Создаем входной и выходной сокеты
        passthrough_group.interface.new_socket("Geometry", in_out='INPUT', socket_type='NodeSocketGeometry')
        passthrough_group.interface.new_socket("Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')
        
        # Создаем узлы внутри группы
        group_input = passthrough_group.nodes.new('NodeGroupInput')
        group_output = passthrough_group.nodes.new('NodeGroupOutput')
        group_input.location = (-200, 0)
        group_output.location = (200, 0)
        
        # Соединяем вход с выходом (passthrough)
        passthrough_group.links.new(group_input.outputs[0], group_output.inputs[0])
        
        effector_node.node_tree = passthrough_group
        print(f"[DEBUG] Создана passthrough группа для placeholder эффектора объектного клонера")
        
    except Exception as e:
        print(f"[ERROR] Не удалось создать placeholder эффектора для объектного клонера: {e}")
