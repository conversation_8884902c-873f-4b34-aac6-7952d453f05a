"""
Collection Cloner Helper Functions

Вспомогательные функции для создания узлов в коллекционных клонерах.
"""

import bpy


def create_collection_info_nodes(nodes, links, group_in, use_anti_recursion):
    """Создает узлы Collection Info и анти-рекурсии"""

    # Collection Info node для получения инстансов коллекции
    collection_info = nodes.new('GeometryNodeCollectionInfo')
    collection_info.inputs["Separate Children"].default_value = False
    collection_info.transform_space = 'RELATIVE'
    collection_info.location = (-800, -200)

    # Connect collection input to Collection Info node
    links.new(group_in.outputs['Collection'], collection_info.inputs['Collection'])

    # Если анти-рекурсия включена, добавляем узлы для её реализации на входе
    if use_anti_recursion:
        # Добавляем узел Realize Instances для входных инстансов
        input_realize = nodes.new('GeometryNodeRealizeInstances')
        input_realize.name = "Input Realize Instances"
        input_realize.location = (-700, -200)

        # Создаем узел Switch для входных инстансов
        input_switch = nodes.new('GeometryNodeSwitch')
        input_switch.input_type = 'GEOMETRY'
        input_switch.name = "Input Realize Switch"
        input_switch.location = (-600, -200)

        # Соединяем коллекцию с узлом Realize Instances
        links.new(collection_info.outputs['Instances'], input_realize.inputs['Geometry'])

        # Настраиваем входной переключатель
        links.new(group_in.outputs['Realize Instances'], input_switch.inputs['Switch'])
        links.new(collection_info.outputs['Instances'], input_switch.inputs[False])
        links.new(input_realize.outputs['Geometry'], input_switch.inputs[True])

        # Используем выход переключателя для дальнейшей логики
        return input_switch.outputs[0]
    else:
        # Если анти-рекурсия выключена, просто используем выход коллекции
        return collection_info.outputs['Instances']


def create_spacing_nodes(nodes, links, group_in, cloner_type):
    """Создает узлы для обработки spacing/offset"""

    if cloner_type == "GRID":
        # GRID cloner uses spacing vector
        spacing_multiplier = nodes.new('ShaderNodeVectorMath')
        spacing_multiplier.operation = 'MULTIPLY'
        spacing_multiplier.inputs[1].default_value = (1.0, 1.0, 1.0)
        spacing_multiplier.location = (-900, 100)
        links.new(group_in.outputs["Spacing"], spacing_multiplier.inputs[0])

        # Separate XYZ to get individual spacing components
        separate_xyz = nodes.new('ShaderNodeSeparateXYZ')
        separate_xyz.location = (-750, 100)
        links.new(spacing_multiplier.outputs[0], separate_xyz.inputs[0])
        return separate_xyz

    elif cloner_type == "LINEAR":
        # LINEAR cloner uses offset vector directly
        offset_vector = group_in.outputs["Offset"]

        # Still need to separate for centering calculations
        separate_xyz = nodes.new('ShaderNodeSeparateXYZ')
        separate_xyz.location = (-750, 100)
        links.new(offset_vector, separate_xyz.inputs[0])
        return separate_xyz

    else:  # CIRCLE
        # CIRCLE uses float radius
        combine_spacing = nodes.new('ShaderNodeCombineXYZ')
        combine_spacing.location = (-900, 100)
        links.new(group_in.outputs["Radius"], combine_spacing.inputs['X'])
        links.new(group_in.outputs["Radius"], combine_spacing.inputs['Y'])
        links.new(group_in.outputs["Radius"], combine_spacing.inputs['Z'])

        spacing_multiplier = nodes.new('ShaderNodeVectorMath')
        spacing_multiplier.operation = 'MULTIPLY'
        spacing_multiplier.inputs[1].default_value = (1.0, 1.0, 1.0)
        spacing_multiplier.location = (-800, 100)
        links.new(combine_spacing.outputs[0], spacing_multiplier.inputs[0])

        # Separate XYZ to get individual spacing components
        separate_xyz = nodes.new('ShaderNodeSeparateXYZ')
        separate_xyz.location = (-750, 100)
        links.new(spacing_multiplier.outputs[0], separate_xyz.inputs[0])
        return separate_xyz


def create_grid_points(nodes, links, group_in, separate_xyz):
    """Создает точки для grid клонера"""

    # Step 1: Create a line of points along X axis with correct spacing
    line_x = nodes.new('GeometryNodeMeshLine')
    line_x.name = "Line X Points"
    if hasattr(line_x, "mode"):
        line_x.mode = 'OFFSET'
    if hasattr(line_x, "count_mode"):
        line_x.count_mode = 'TOTAL'
    line_x.location = (-700, 300)
    links.new(group_in.outputs['Count X'], line_x.inputs['Count'])

    # Create offset vector for X axis (Spacing X, 0, 0)
    combine_x_offset = nodes.new('ShaderNodeCombineXYZ')
    combine_x_offset.location = (-800, 300)
    links.new(separate_xyz.outputs['X'], combine_x_offset.inputs['X'])
    combine_x_offset.inputs['Y'].default_value = 0.0
    combine_x_offset.inputs['Z'].default_value = 0.0

    # Connect offset to line
    if "Offset" in line_x.inputs:
        links.new(combine_x_offset.outputs['Vector'], line_x.inputs['Offset'])

    # Step 2: Create a line of points along Y axis with correct spacing
    line_y = nodes.new('GeometryNodeMeshLine')
    line_y.name = "Line Y Points"
    if hasattr(line_y, "mode"):
        line_y.mode = 'OFFSET'
    if hasattr(line_y, "count_mode"):
        line_y.count_mode = 'TOTAL'
    line_y.location = (-700, 200)
    links.new(group_in.outputs['Count Y'], line_y.inputs['Count'])

    # Create offset vector for Y axis (0, Spacing Y, 0)
    combine_y_offset = nodes.new('ShaderNodeCombineXYZ')
    combine_y_offset.location = (-800, 200)
    combine_y_offset.inputs['X'].default_value = 0.0
    links.new(separate_xyz.outputs['Y'], combine_y_offset.inputs['Y'])
    combine_y_offset.inputs['Z'].default_value = 0.0

    # Connect offset to line
    if "Offset" in line_y.inputs:
        links.new(combine_y_offset.outputs['Vector'], line_y.inputs['Offset'])

    # Convert lines to points for instancing
    points_x = nodes.new('GeometryNodeMeshToPoints')
    points_x.location = (-600, 300)
    links.new(line_x.outputs['Mesh'], points_x.inputs['Mesh'])

    # Step 3: Instance line_x along line_y to create a 2D grid
    instance_x_on_y = nodes.new('GeometryNodeInstanceOnPoints')
    instance_x_on_y.name = "Instance X on Y"
    instance_x_on_y.location = (-500, 250)
    links.new(line_y.outputs['Mesh'], instance_x_on_y.inputs['Points'])
    links.new(line_x.outputs['Mesh'], instance_x_on_y.inputs['Instance'])

    # Realize the 2D grid instances
    realize_2d_grid = nodes.new('GeometryNodeRealizeInstances')
    realize_2d_grid.name = "Realize 2D Grid"
    realize_2d_grid.location = (-400, 250)
    links.new(instance_x_on_y.outputs['Instances'], realize_2d_grid.inputs['Geometry'])

    # Step 4: Create a line along Z axis with correct spacing
    line_z = nodes.new('GeometryNodeMeshLine')
    line_z.name = "Line Z Points"
    if hasattr(line_z, "mode"):
        line_z.mode = 'OFFSET'
    if hasattr(line_z, "count_mode"):
        line_z.count_mode = 'TOTAL'
    line_z.location = (-700, 100)
    links.new(group_in.outputs['Count Z'], line_z.inputs['Count'])

    # Create offset vector for Z axis (0, 0, Spacing Z)
    combine_z_offset = nodes.new('ShaderNodeCombineXYZ')
    combine_z_offset.location = (-800, 100)
    combine_z_offset.inputs['X'].default_value = 0.0
    combine_z_offset.inputs['Y'].default_value = 0.0
    links.new(separate_xyz.outputs['Z'], combine_z_offset.inputs['Z'])

    # Connect offset to line
    if "Offset" in line_z.inputs:
        links.new(combine_z_offset.outputs['Vector'], line_z.inputs['Offset'])

    # Step 5: Instance the 2D grid along the Z line to create a 3D grid
    instance_2d_on_z = nodes.new('GeometryNodeInstanceOnPoints')
    instance_2d_on_z.name = "Instance 2D on Z"
    instance_2d_on_z.location = (-300, 200)
    links.new(line_z.outputs['Mesh'], instance_2d_on_z.inputs['Points'])
    links.new(realize_2d_grid.outputs['Geometry'], instance_2d_on_z.inputs['Instance'])

    # Realize the 3D grid instances
    realize_3d_grid = nodes.new('GeometryNodeRealizeInstances')
    realize_3d_grid.name = "Realize 3D Grid"
    realize_3d_grid.location = (-200, 200)
    links.new(instance_2d_on_z.outputs['Instances'], realize_3d_grid.inputs['Geometry'])

    # Switch between 2D grid (if Count Z = 1) and 3D grid (if Count Z > 1)
    compare_z_count = nodes.new('FunctionNodeCompare')
    compare_z_count.data_type = 'INT'
    compare_z_count.operation = 'GREATER_THAN'
    compare_z_count.inputs[3].default_value = 1  # Compare with 1
    compare_z_count.location = (-300, 100)
    links.new(group_in.outputs['Count Z'], compare_z_count.inputs[2])  # Input A

    switch_points = nodes.new('GeometryNodeSwitch')
    switch_points.name = "Switch 2D/3D Points"
    switch_points.input_type = 'GEOMETRY'
    switch_points.location = (-100, 150)

    # ИСПРАВЛЕНИЕ: Убираем связь realize_2d_grid -> switch_2d_3d_points.Switch (как в AFTER.py)
    # Оставляем только управление через compare и подключение к False/True входам
    links.new(compare_z_count.outputs['Result'], switch_points.inputs['Switch'])
    # ВАЖНО: В AFTER.py НЕТ связи realize_2d_grid -> switch.Switch!
    # links.new(realize_2d_grid.outputs['Geometry'], switch_points.inputs['Switch'])  # УДАЛЕНО
    links.new(realize_2d_grid.outputs['Geometry'], switch_points.inputs[False])  # Use 2D if Count Z = 1
    links.new(realize_3d_grid.outputs['Geometry'], switch_points.inputs[True])  # Use 3D if Count Z > 1

    # Centering logic будет добавлена в следующем файле
    # Пока возвращаем базовые точки
    final_points = nodes.new('GeometryNodeMeshToPoints')
    final_points.location = (300, 100)
    links.new(switch_points.outputs[0], final_points.inputs['Mesh'])

    return final_points.outputs['Points']


def create_linear_points(nodes, links, group_in, separate_xyz):
    """Создает точки для linear клонера"""

    # Create line for linear layout
    line_node = nodes.new('GeometryNodeMeshLine')
    if hasattr(line_node, "mode"):
        line_node.mode = 'OFFSET'
    if hasattr(line_node, "count_mode"):
        line_node.count_mode = 'TOTAL'
    line_node.location = (-600, 200)

    # Link line parameters - use Count instead of Count X
    links.new(group_in.outputs['Count'], line_node.inputs['Count'])

    # Connect offset vector directly from input
    if "Offset" in line_node.inputs:
        links.new(group_in.outputs['Offset'], line_node.inputs['Offset'])
    elif "Resolution" in line_node.inputs:
        # Fallback for older versions - extract X component
        links.new(separate_xyz.outputs['X'], line_node.inputs['Resolution'])
    elif "Length" in line_node.inputs:
        # Fallback for older versions - extract X component
        links.new(separate_xyz.outputs['X'], line_node.inputs['Length'])

    # Centering logic (упрощенная версия)
    # Calculate total size: (Count - 1) * Offset X component
    count_minus_one = nodes.new('ShaderNodeMath')
    count_minus_one.operation = 'SUBTRACT'
    count_minus_one.inputs[1].default_value = 1.0
    count_minus_one.location = (-400, 0)
    links.new(group_in.outputs['Count'], count_minus_one.inputs[0])

    total_size = nodes.new('ShaderNodeMath')
    total_size.operation = 'MULTIPLY'
    total_size.location = (-300, 0)
    links.new(count_minus_one.outputs['Value'], total_size.inputs[0])
    links.new(separate_xyz.outputs['X'], total_size.inputs[1])

    # Calculate center offset (half of total size)
    center_offset_value = nodes.new('ShaderNodeMath')
    center_offset_value.operation = 'DIVIDE'
    center_offset_value.inputs[1].default_value = 2.0
    center_offset_value.location = (-200, 0)
    links.new(total_size.outputs['Value'], center_offset_value.inputs[0])

    # Create vector for centering (offset only in X direction)
    center_offset = nodes.new('ShaderNodeCombineXYZ')
    center_offset.location = (-100, 0)
    links.new(center_offset_value.outputs['Value'], center_offset.inputs['X'])
    center_offset.inputs['Y'].default_value = 0.0
    center_offset.inputs['Z'].default_value = 0.0

    # Negate for correct offset direction
    negate_center = nodes.new('ShaderNodeVectorMath')
    negate_center.operation = 'MULTIPLY'
    negate_center.inputs[1].default_value = (-1.0, -1.0, -1.0)
    negate_center.location = (0, 0)
    links.new(center_offset.outputs['Vector'], negate_center.inputs[0])

    # Create zero vector for non-centered option
    zero_vector = nodes.new('ShaderNodeCombineXYZ')
    zero_vector.inputs[0].default_value = 0.0
    zero_vector.inputs[1].default_value = 0.0
    zero_vector.inputs[2].default_value = 0.0
    zero_vector.location = (0, -50)

    # Switch between centered and non-centered
    center_switch = nodes.new('GeometryNodeSwitch')
    center_switch.input_type = 'VECTOR'
    center_switch.location = (100, 0)
    links.new(group_in.outputs['Center Grid'], center_switch.inputs[0])  # Switch
    links.new(zero_vector.outputs['Vector'], center_switch.inputs[False])  # No centering
    links.new(negate_center.outputs['Vector'], center_switch.inputs[True])  # With centering

    # Set position for centering
    set_position = nodes.new('GeometryNodeSetPosition')
    set_position.location = (200, 200)
    links.new(line_node.outputs['Mesh'], set_position.inputs['Geometry'])
    links.new(center_switch.outputs[0], set_position.inputs['Offset'])

    # Convert to points
    mesh_to_points = nodes.new('GeometryNodeMeshToPoints')
    mesh_to_points.location = (300, 200)
    links.new(set_position.outputs['Geometry'], mesh_to_points.inputs['Mesh'])

    return mesh_to_points.outputs['Points']


def create_circle_points(nodes, links, group_in):
    """Создает точки для circle клонера"""

    # Создаем круг точек
    circle_node = nodes.new('GeometryNodeMeshCircle')
    circle_node.location = (-400, 200)

    # Соединяем параметры круга
    links.new(group_in.outputs['Count'], circle_node.inputs['Vertices'])
    links.new(group_in.outputs['Radius'], circle_node.inputs['Radius'])

    # Преобразуем меш в точки
    mesh_to_points = nodes.new('GeometryNodeMeshToPoints')
    mesh_to_points.location = (-200, 200)
    links.new(circle_node.outputs['Mesh'], mesh_to_points.inputs['Mesh'])

    return mesh_to_points.outputs['Points']


def create_instance_logic(nodes, links, group_in, point_source, collection_output, use_anti_recursion):
    """Создает логику инстансирования и трансформации"""

    # --- Random Transform Logic ---
    # Get point indices for random generation
    index_node = nodes.new('GeometryNodeInputIndex')
    index_node.location = (350, -100)

    # Random values nodes
    # Position randomization
    random_pos_node = nodes.new('FunctionNodeRandomValue')
    random_pos_node.data_type = 'FLOAT_VECTOR'
    random_pos_node.location = (400, -150)

    # Create negative range for position
    vector_math_neg_pos = nodes.new('ShaderNodeVectorMath')
    vector_math_neg_pos.operation = 'MULTIPLY'
    vector_math_neg_pos.inputs[1].default_value = (-1.0, -1.0, -1.0)
    vector_math_neg_pos.location = (300, -150)
    links.new(group_in.outputs['Random Position'], vector_math_neg_pos.inputs[0])

    # Connect to random node
    links.new(group_in.outputs['Random Seed'], random_pos_node.inputs['Seed'])
    links.new(index_node.outputs['Index'], random_pos_node.inputs['ID'])
    links.new(vector_math_neg_pos.outputs['Vector'], random_pos_node.inputs['Min'])
    links.new(group_in.outputs['Random Position'], random_pos_node.inputs['Max'])

    # Rotation randomization
    random_rot_node = nodes.new('FunctionNodeRandomValue')
    random_rot_node.data_type = 'FLOAT_VECTOR'
    random_rot_node.location = (400, -250)

    # Create negative range for rotation
    vector_math_neg_rot = nodes.new('ShaderNodeVectorMath')
    vector_math_neg_rot.operation = 'MULTIPLY'
    vector_math_neg_rot.inputs[1].default_value = (-1.0, -1.0, -1.0)
    vector_math_neg_rot.location = (300, -250)
    links.new(group_in.outputs['Random Rotation'], vector_math_neg_rot.inputs[0])

    # Connect to random node
    links.new(group_in.outputs['Random Seed'], random_rot_node.inputs['Seed'])
    links.new(index_node.outputs['Index'], random_rot_node.inputs['ID'])
    links.new(vector_math_neg_rot.outputs['Vector'], random_rot_node.inputs['Min'])
    links.new(group_in.outputs['Random Rotation'], random_rot_node.inputs['Max'])

    # Scale randomization
    random_scale_node = nodes.new('FunctionNodeRandomValue')
    random_scale_node.data_type = 'FLOAT'  # Single float for uniform scaling
    random_scale_node.location = (400, -350)

    # Create negative range for scale
    math_neg_scale = nodes.new('ShaderNodeMath')
    math_neg_scale.operation = 'MULTIPLY'
    math_neg_scale.inputs[1].default_value = -1.0
    math_neg_scale.location = (300, -350)
    links.new(group_in.outputs['Random Scale'], math_neg_scale.inputs[0])

    # Connect to random node
    links.new(group_in.outputs['Random Seed'], random_scale_node.inputs['Seed'])
    links.new(index_node.outputs['Index'], random_scale_node.inputs['ID'])
    links.new(math_neg_scale.outputs['Value'], random_scale_node.inputs['Min'])
    links.new(group_in.outputs['Random Scale'], random_scale_node.inputs['Max'])

    # Instance on Points node - this will place the collection on each point
    instance_node = nodes.new('GeometryNodeInstanceOnPoints')
    instance_node.location = (400, 100)

    # Connect the generated points to the instances
    links.new(point_source, instance_node.inputs['Points'])

    # Connect the object geometry
    links.new(collection_output, instance_node.inputs['Instance'])

    # --- Apply Transforms to Instances ---
    # 1. Apply random position
    translate_instances = nodes.new('GeometryNodeTranslateInstances')
    translate_instances.location = (500, 100)
    links.new(instance_node.outputs['Instances'], translate_instances.inputs['Instances'])
    links.new(random_pos_node.outputs['Value'], translate_instances.inputs['Translation'])

    # 2. Apply rotation (base + random)
    add_random_rotation = nodes.new('ShaderNodeVectorMath')
    add_random_rotation.operation = 'ADD'
    add_random_rotation.location = (500, 0)
    links.new(group_in.outputs['Instance Rotation'], add_random_rotation.inputs[0])
    links.new(random_rot_node.outputs['Value'], add_random_rotation.inputs[1])

    rotate_instances = nodes.new('GeometryNodeRotateInstances')
    rotate_instances.location = (600, 100)
    links.new(translate_instances.outputs['Instances'], rotate_instances.inputs['Instances'])
    links.new(add_random_rotation.outputs['Vector'], rotate_instances.inputs['Rotation'])

    # 3. Apply scale (base + random)
    combine_xyz_scale = nodes.new('ShaderNodeCombineXYZ')
    combine_xyz_scale.location = (600, -100)
    links.new(random_scale_node.outputs['Value'], combine_xyz_scale.inputs['X'])
    links.new(random_scale_node.outputs['Value'], combine_xyz_scale.inputs['Y'])
    links.new(random_scale_node.outputs['Value'], combine_xyz_scale.inputs['Z'])

    add_random_scale = nodes.new('ShaderNodeVectorMath')
    add_random_scale.operation = 'ADD'
    add_random_scale.location = (600, -50)
    links.new(group_in.outputs['Instance Scale'], add_random_scale.inputs[0])
    links.new(combine_xyz_scale.outputs['Vector'], add_random_scale.inputs[1])

    scale_instances = nodes.new('GeometryNodeScaleInstances')
    scale_instances.location = (700, 100)
    links.new(rotate_instances.outputs['Instances'], scale_instances.inputs['Instances'])
    links.new(add_random_scale.outputs['Vector'], scale_instances.inputs['Scale'])

    # Apply global transforms
    global_transform = nodes.new('GeometryNodeTransform')
    global_transform.location = (800, 100)
    links.new(scale_instances.outputs['Instances'], global_transform.inputs['Geometry'])
    links.new(group_in.outputs['Global Position'], global_transform.inputs['Translation'])
    links.new(group_in.outputs['Global Rotation'], global_transform.inputs['Rotation'])

    # Если анти-рекурсия включена, добавляем узлы для её реализации на выходе
    if use_anti_recursion:
        # ИСПРАВЛЕНИЕ: Создаем placeholder для эффектора между Transform Geometry и Final Realize Instances
        # Это обеспечивает правильную схему подключения как в AFTER.py

        # Создаем узел эффектора-заглушки (будет заменен при применении реального эффектора)
        effector_placeholder = nodes.new('GeometryNodeGroup')
        effector_placeholder.name = "Effector_Random_Effector"
        effector_placeholder.location = (900, 100)

        # Пытаемся найти и установить node_tree для Random Effector
        try:
            # Ищем существующую группу Random Effector
            random_effector_group = None
            for group in bpy.data.node_groups:
                if "random" in group.name.lower() and "effector" in group.name.lower():
                    random_effector_group = group
                    break

            if random_effector_group:
                effector_placeholder.node_tree = random_effector_group
                print(f"[DEBUG] Установлена node_tree для эффектора: {random_effector_group.name}")
            else:
                print(f"[DEBUG] Random Effector group не найдена, создаем placeholder без node_tree")
        except Exception as e:
            print(f"[DEBUG] Не удалось установить node_tree для эффектора: {e}")

        # Создаем узел Realize Instances для финального выхода
        final_realize = nodes.new('GeometryNodeRealizeInstances')
        final_realize.name = "Final Realize Instances"
        final_realize.location = (1000, 0)

        # Создаем узел Switch для финального выхода
        final_switch = nodes.new('GeometryNodeSwitch')
        final_switch.input_type = 'GEOMETRY'
        final_switch.name = "Final Realize Switch"
        final_switch.location = (1050, 0)

        # ПРАВИЛЬНАЯ СХЕМА ПОДКЛЮЧЕНИЯ (точно как в AFTER.py):
        # 1. Transform Geometry -> Effector (строка 535 в AFTER)
        links.new(global_transform.outputs['Geometry'], effector_placeholder.inputs[0])

        # 2. Effector -> Final Realize Instances (строка 529 в AFTER)
        links.new(effector_placeholder.outputs[0], final_realize.inputs['Geometry'])

        # 3. Final Realize Instances -> Final Realize Switch.False (строка 531 в AFTER)
        links.new(final_realize.outputs['Geometry'], final_switch.inputs['False'])

        # 4. Group Input.Realize Instances -> Final Realize Switch.Switch
        # ВАЖНО: В AFTER.py НЕТ прямой связи transform_geometry -> final_realize_switch.Switch!
        # Вместо этого должна быть связь с group_input.Realize_Instances
        links.new(group_in.outputs['Realize Instances'], final_switch.inputs['Switch'])

        return final_switch.outputs[0]
    else:
        # Если анти-рекурсия выключена, просто возвращаем глобальный трансформ
        return global_transform.outputs['Geometry']