"""
Collection Cloner Logic Module

Этот модуль содержит логику создания нодовых групп для коллекционных клонеров.
Перенесено из core/utils/cloner_operations/collection_cloner.py для устранения зависимости.
"""

import bpy


def create_collection_cloner_logic(cloner_type, name_suffix="", collection_obj=None, use_anti_recursion=False):
    """
    Создает логику нодовой группы для коллекционного клонера.

    Эта функция содержит всю логику, которая раньше была в
    core/utils/cloner_operations/collection_cloner.py

    Args:
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        name_suffix: Суффикс для имени группы
        collection_obj: Коллекция для клонирования (может быть None для шаблона)
        use_anti_recursion: Использовать анти-рекурсию

    Returns:
        Созданная нодовая группа
    """
    # Create a new geometry node group
    node_group_name = f"CollectionCloner_{cloner_type}{name_suffix}"
    counter = 1
    while node_group_name in bpy.data.node_groups:
        node_group_name = f"CollectionCloner_{cloner_type}_{counter:03d}{name_suffix}"
        counter += 1

    node_group = bpy.data.node_groups.new(node_group_name, 'GeometryNodeTree')

    # Create interface
    _create_interface(node_group, cloner_type, collection_obj, use_anti_recursion)

    # Create nodes
    _create_nodes(node_group, cloner_type, use_anti_recursion)

    return node_group


def _create_interface(node_group, cloner_type, collection_obj, use_anti_recursion):
    """Создает интерфейс нодовой группы"""

    # Layout counts
    if cloner_type == "LINEAR":
        count_socket = node_group.interface.new_socket("Count", in_out='INPUT', socket_type='NodeSocketInt')
    elif cloner_type == "CIRCLE":
        count_socket = node_group.interface.new_socket("Count", in_out='INPUT', socket_type='NodeSocketInt')
    else:  # GRID
        count_x_socket = node_group.interface.new_socket("Count X", in_out='INPUT', socket_type='NodeSocketInt')

    # Different parameters based on cloner type
    if cloner_type == "GRID":
        count_y_socket = node_group.interface.new_socket("Count Y", in_out='INPUT', socket_type='NodeSocketInt')
        count_z_socket = node_group.interface.new_socket("Count Z", in_out='INPUT', socket_type='NodeSocketInt')

    # Spacing/Offset parameter
    if cloner_type == "GRID":
        spacing_socket = node_group.interface.new_socket("Spacing", in_out='INPUT', socket_type='NodeSocketVector')
    elif cloner_type == "LINEAR":
        offset_socket = node_group.interface.new_socket("Offset", in_out='INPUT', socket_type='NodeSocketVector')
    else:  # CIRCLE
        radius_socket = node_group.interface.new_socket("Radius", in_out='INPUT', socket_type='NodeSocketFloat')

    # Instance Transform
    rotation_socket = node_group.interface.new_socket("Instance Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    scale_socket = node_group.interface.new_socket("Instance Scale", in_out='INPUT', socket_type='NodeSocketVector')

    # Global transform parameters
    global_pos_socket = node_group.interface.new_socket("Global Position", in_out='INPUT', socket_type='NodeSocketVector')
    global_rot_socket = node_group.interface.new_socket("Global Rotation", in_out='INPUT', socket_type='NodeSocketVector')

    # Random parameters
    random_seed_socket = node_group.interface.new_socket("Random Seed", in_out='INPUT', socket_type='NodeSocketInt')
    random_pos_socket = node_group.interface.new_socket("Random Position", in_out='INPUT', socket_type='NodeSocketVector')
    random_rot_socket = node_group.interface.new_socket("Random Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    random_scale_socket = node_group.interface.new_socket("Random Scale", in_out='INPUT', socket_type='NodeSocketFloat')

    # Material/Color parameters
    color_socket = node_group.interface.new_socket("Color", in_out='INPUT', socket_type='NodeSocketColor')

    # Extra options
    center_grid_socket = node_group.interface.new_socket("Center Grid", in_out='INPUT', socket_type='NodeSocketBool')
    pick_instance_socket = node_group.interface.new_socket("Pick Random Instance", in_out='INPUT', socket_type='NodeSocketBool')

    # Use Effector parameter for effector integration
    use_effector_socket = node_group.interface.new_socket("Use Effector", in_out='INPUT', socket_type='NodeSocketBool')

    # Добавляем параметр для включения/выключения "реализации" инстансов
    realize_instances_socket = node_group.interface.new_socket("Realize Instances", in_out='INPUT', socket_type='NodeSocketBool')

    # Collection input socket - для работы с коллекциями
    collection_socket = node_group.interface.new_socket("Collection", in_out='INPUT', socket_type='NodeSocketCollection')

    # Output socket for the final geometry
    node_group.interface.new_socket("Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')

    # Set default values
    try:
        realize_instances_socket.default_value = use_anti_recursion

        # Layout defaults
        if cloner_type == "GRID":
            count_x_socket.default_value = 3
            count_x_socket.min_value = 1
            count_x_socket.max_value = 100
        elif cloner_type == "LINEAR":
            count_socket.default_value = 5
            count_socket.min_value = 1
            count_socket.max_value = 1000
        elif cloner_type == "CIRCLE":
            count_socket.default_value = 8
            count_socket.min_value = 3
            count_socket.max_value = 1000

        if cloner_type == "GRID":
            count_y_socket.default_value = 3
            count_y_socket.min_value = 1
            count_y_socket.max_value = 100

            count_z_socket.default_value = 1
            count_z_socket.min_value = 1
            count_z_socket.max_value = 100

        # Set spacing/offset defaults
        if cloner_type == "GRID":
            spacing_socket.default_value = (3.0, 3.0, 3.0)
        elif cloner_type == "LINEAR":
            offset_socket.default_value = (3.0, 0.0, 0.0)
        else:  # CIRCLE
            radius_socket.default_value = 4.0

        # Transform defaults
        rotation_socket.default_value = (0.0, 0.0, 0.0)
        rotation_socket.subtype = 'EULER'
        scale_socket.default_value = (1.0, 1.0, 1.0)

        # Global transform defaults
        global_pos_socket.default_value = (0.0, 0.0, 0.0)
        global_rot_socket.default_value = (0.0, 0.0, 0.0)
        global_rot_socket.subtype = 'EULER'

        # Random defaults
        random_seed_socket.default_value = 0
        random_seed_socket.min_value = 0
        random_seed_socket.max_value = 10000

        random_pos_socket.default_value = (0.0, 0.0, 0.0)
        random_rot_socket.default_value = (0.0, 0.0, 0.0)
        random_rot_socket.subtype = 'EULER'

        random_scale_socket.default_value = 0.0
        random_scale_socket.min_value = 0.0
        random_scale_socket.max_value = 1.0

        # Color default
        color_socket.default_value = (1.0, 1.0, 1.0, 1.0)

        # Options defaults
        center_grid_socket.default_value = True
        pick_instance_socket.default_value = False
        use_effector_socket.default_value = True

        # Collection default
        if collection_obj:
            collection_socket.default_value = collection_obj

    except Exception as e:
        print(f"Warning: Could not set default values for sockets: {e}")


def _create_nodes(node_group, cloner_type, use_anti_recursion):
    """Создает узлы нодовой группы"""
    from .collection_helpers import (
        create_collection_info_nodes,
        create_spacing_nodes,
        create_grid_points,
        create_linear_points,
        create_circle_points,
        create_instance_logic
    )

    nodes = node_group.nodes
    links = node_group.links

    # Group input/output nodes
    group_in = nodes.new('NodeGroupInput')
    group_in.location = (-1000, 0)
    group_out = nodes.new('NodeGroupOutput')
    group_out.location = (1000, 0)

    # Collection Info nodes
    collection_output, input_realize_node = create_collection_info_nodes(nodes, links, group_in, use_anti_recursion)

    # Spacing/offset processing nodes
    separate_xyz = create_spacing_nodes(nodes, links, group_in, cloner_type)

    # Create points based on cloner type
    if cloner_type == "GRID":
        point_source = create_grid_points(nodes, links, group_in, separate_xyz)
    elif cloner_type == "LINEAR":
        point_source = create_linear_points(nodes, links, group_in, separate_xyz)
    else:  # CIRCLE
        point_source = create_circle_points(nodes, links, group_in)

    # Create final instance logic
    final_output = create_instance_logic(nodes, links, group_in, point_source, collection_output, use_anti_recursion, input_realize_node)

    # Connect final output
    links.new(final_output, group_out.inputs['Geometry'])
