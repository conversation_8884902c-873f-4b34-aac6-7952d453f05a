"""
Фабрика для создания объектных клонеров.

Этот модуль содержит основную логику координации создания клонеров,
объединяя все компоненты системы.

"""

import bpy
from .object_manager import (
    create_cloner_object,
    create_cloner_collection,
    create_modifier,
    add_object_to_collection
)
from .chain_manager import setup_chain_properties
from ..common.naming import generate_node_group_name
from ..common.visibility import (
    setup_cloner_visibility,
    setup_collection_visibility,
    hide_original_source,
    ensure_chain_visibility
)
from ..builders import build_node_structure
# Импорт из новой модульной системы stacked_cloner_modules
from .stacked_factory import create_standard_stacked_cloner
from ....operations.helpers.parameter_setup.universal_params import setup_cloner_params

def create_object_cloner(context, cloner_type, orig_obj, use_stacked_modifiers=False, use_custom_group=True):
    """
    Создает клонер для объекта.

    Args:
        context: Контекст Blender
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        orig_obj: Исходный объект для клонирования
        use_stacked_modifiers: Использовать стековые модификаторы
        use_custom_group: Использовать кастомную группу узлов

    Returns:
        bool: True если клонер успешно создан, False в случае ошибки
    """
    # Проверяем режим работы - стековые модификаторы или обычные клонеры
    is_stacked_mode = use_stacked_modifiers
    print(f"DEBUG: use_stacked_modifiers = {use_stacked_modifiers}, is_stacked_mode = {is_stacked_mode}")

    # В зависимости от режима вызываем соответствующую функцию
    if is_stacked_mode:
        # Создаем стековый клонер на том же объекте
        modifier, success = create_standard_stacked_cloner(context, cloner_type, orig_obj)
        return success
    else:
        # Создаем обычный клонер (новый объект с модификатором)
        success = create_standard_object_cloner(context, cloner_type, orig_obj, use_custom_group)
        return success

def create_standard_object_cloner(context, cloner_type, orig_obj, use_custom_group=True):
    """
    Создает обычный (не стековый) клонер для объекта.

    Args:
        context: Контекст Blender
        cloner_type: Тип клонера (GRID, LINEAR, CIRCLE)
        orig_obj: Исходный объект для клонирования
        use_custom_group: Использовать кастомную группу узлов

    Returns:
        bool: True если клонер успешно создан, False в случае ошибки
    """
    try:
        # Создаем объект клонера (используем object_manager)
        cloner_obj = create_cloner_object(orig_obj, cloner_type)
        if not cloner_obj:
            print("Ошибка при создании объекта клонера")
            return False

        # Создаем коллекцию для клонера (используем object_manager)
        cloner_collection = create_cloner_collection(context, orig_obj, cloner_type)
        if not cloner_collection:
            print("Ошибка при создании коллекции клонера")
            return False

        # Добавляем клонер-объект в коллекцию (используем object_manager)
        if not add_object_to_collection(cloner_obj, cloner_collection):
            print("Ошибка при добавлении объекта в коллекцию")
            return False

        # Настраиваем видимость (используем новые утилиты)
        setup_collection_visibility(context, cloner_collection)
        setup_cloner_visibility(cloner_obj)
        ensure_chain_visibility(context, orig_obj)

        # Создаем модификатор для клонера (используем object_manager)
        modifier = create_modifier(cloner_obj, cloner_type)
        if not modifier:
            print("Ошибка при создании модификатора клонера")
            return False

        # Создаем node группу для клонирования объекта (используем новую утилиту)
        node_group_name = generate_node_group_name(orig_obj.name, cloner_type, "object")

        # Создаем новую node группу
        node_group = bpy.data.node_groups.new(node_group_name, 'GeometryNodeTree')

        # Настраиваем структуру узлов (используем node_builders)
        if not build_node_structure(cloner_type, node_group, orig_obj):
            print("Ошибка при создании структуры узлов")
            return False

        # НЕ применяем apply_anti_recursion_to_cloner для новых клонеров
        # Новые построители узлов уже создают правильную структуру анти-рекурсии
        # apply_anti_recursion_to_cloner предназначен для исправления старых клонеров

        # Устанавливаем свойства для клонера
        modifier["is_chained_cloner"] = True
        modifier["source_type"] = "OBJECT"
        modifier["cloner_collection"] = cloner_collection.name

        # Устанавливаем группу узлов для модификатора
        modifier.node_group = node_group

        # Инициализируем список эффекторов
        node_group["linked_effectors"] = []

        # Сохраняем тип источника и другие метаданные
        modifier["source_type"] = "OBJECT"
        modifier["original_object"] = orig_obj.name
        modifier["cloner_collection"] = cloner_collection.name

        print(f"CLONER DEBUG: Создаем клонер для объекта {orig_obj.name}")
        print(f"CLONER DEBUG: Установлен original_object = {orig_obj.name}")

        # Сохраняем состояние видимости оригинального объекта
        modifier["original_hide_viewport"] = orig_obj.hide_viewport
        modifier["original_hide_render"] = orig_obj.hide_render

        # Скрываем оригинальный объект (используем унифицированную функцию)
        hide_original_source(context, orig_obj, mode="object")

        # Делаем клонер-объект активным
        for obj in context.selected_objects:
            obj.select_set(False)
        cloner_obj.select_set(True)
        context.view_layer.objects.active = cloner_obj

        # Настраиваем свойства цепочки клонеров (используем chain_manager)
        if not setup_chain_properties(modifier, orig_obj, cloner_obj):
            print("Ошибка при настройке свойств цепочки")
            # Продолжаем выполнение, так как это не критическая ошибка

        # ТЕПЕРЬ устанавливаем параметры клонера (после установки chain_source_object)
        print(f"PARAMS DEBUG: Устанавливаем параметры для {cloner_type} клонера")
        print(f"PARAMS DEBUG: chain_source_object = {modifier.get('chain_source_object', 'НЕ УСТАНОВЛЕН')}")

        # Устанавливаем параметры клонера через универсальную систему
        setup_cloner_params(modifier, cloner_type)

        # Обновляем UI
        context.view_layer.update()

        return True

    except Exception as e:
        print(f"Error creating object cloner: {e}")
        return False
