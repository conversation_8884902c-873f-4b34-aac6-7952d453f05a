"""
Collection cloner effector application.

This module implements the support for applying effectors to collection cloners.
"""

import bpy


def _set_effector_node_defaults(effector_node):
    """
    Устанавливает стандартные параметры для узла эффектора.

    Args:
        effector_node: Узел эффектора в нодовой группе клонера
    """
    # Проверяем наличие параметра Enable и устанавливаем его в True
    if hasattr(effector_node, 'inputs') and 'Enable' in effector_node.inputs:
        effector_node.inputs['Enable'].default_value = True
        print(f"[DEBUG] Установлен параметр Enable=True для эффектора {effector_node.name}")

    # Также устанавливаем параметр Strength = 1.0, если он есть
    if hasattr(effector_node, 'inputs') and 'Strength' in effector_node.inputs:
        effector_node.inputs['Strength'].default_value = 1.0
        print(f"[DEBUG] Установлен параметр Strength=1.0 для эффектора {effector_node.name}")


def apply_effector_to_collection_cloner(obj, cloner_mod, effector_mod):
    """
    Применяет эффектор к клонеру коллекций.

    Args:
        obj: Объект с клонером коллекций
        cloner_mod: Модификатор клонера коллекций
        effector_mod: Модификатор эффектора

    Returns:
        bool: True если эффектор успешно применен
    """
    try:
        # Получаем нод-группы клонера и эффектора
        cloner_group = cloner_mod.node_group
        effector_group = effector_mod.node_group
        if not cloner_group or not effector_group:
            print(f"[ERROR] apply_effector_to_collection_cloner: Нет нод-группы в клонере или эффекторе")
            return False

        print(f"[DEBUG] apply_effector_to_collection_cloner: Применяем эффектор '{effector_mod.name}' к клонеру коллекций '{cloner_mod.name}'")

        # Получаем узлы и связи клонера
        nodes = cloner_group.nodes
        links = cloner_group.links

        # Ищем выходной узел группы
        group_output = None
        for node in nodes:
            if node.type == 'GROUP_OUTPUT':
                group_output = node
                break

        if not group_output:
            print(f"[ERROR] apply_effector_to_collection_cloner: Не найден Group Output в клонере '{cloner_mod.name}'")
            return False

        # КРИТИЧНО: Ищем узлы Transform Geometry и Realize Instances
        # Эффектор ВСЕГДА должен быть между ними при включенной анти-рекурсии
        transform_geometry_node = None
        realize_instances_node = None
        anti_recursion_switch = None
        source_node = None
        source_socket = None

        # Ищем узел Transform Geometry
        for node in nodes:
            if (node.type == 'GEOMETRY_NODE_TRANSFORM_GEOMETRY' or
                node.name == "Transform Geometry" or
                "Transform" in node.name):
                transform_geometry_node = node
                print(f"[DEBUG] Найден Transform Geometry: {node.name}")
                break

        # Ищем узел Realize Instances
        for node in nodes:
            if node.type == 'GEOMETRY_NODE_REALIZE_INSTANCES':
                realize_instances_node = node
                print(f"[DEBUG] Найден Realize Instances: {node.name}")
                break

        # Ищем узел Anti-Recursion Switch
        for node in nodes:
            if node.name == "Anti-Recursion Switch":
                anti_recursion_switch = node
                print(f"[DEBUG] Найден Anti-Recursion Switch")
                break

        # ОСНОВНАЯ ЛОГИКА: При наличии Transform и Realize - эффектор между ними
        if transform_geometry_node and realize_instances_node:
            # Проверяем связь между Transform и Realize (напрямую или через анти-рекурсию)
            transform_connected_to_realize = False

            for link in links:
                if link.from_node == transform_geometry_node:
                    # Прямое подключение к Realize Instances
                    if link.to_node == realize_instances_node:
                        transform_connected_to_realize = True
                        break
                    # Подключение через Anti-Recursion Switch
                    elif link.to_node == anti_recursion_switch:
                        # Проверяем, что анти-рекурсия ведет к Realize Instances
                        for link2 in links:
                            if (link2.from_node == anti_recursion_switch and
                                link2.to_node == realize_instances_node):
                                transform_connected_to_realize = True
                                break
                        if transform_connected_to_realize:
                            break

            if transform_connected_to_realize:
                # Используем Transform Geometry как источник
                source_node = transform_geometry_node
                for output in transform_geometry_node.outputs:
                    if output.type == 'GEOMETRY':
                        source_socket = output
                        break
                print(f"[DEBUG] Используем Transform Geometry как источник для эффектора")
            else:
                print(f"[DEBUG] Transform не подключен к Realize, ищем альтернативный источник")

        # Fallback: если не нашли Transform или связь, ищем источник для Realize Instances
        if not source_node and realize_instances_node:
            for link in links:
                if link.to_node == realize_instances_node and link.to_socket.name == 'Geometry':
                    source_node = link.from_node
                    source_socket = link.from_socket
                    print(f"[DEBUG] Найден источник для Realize Instances: {source_node.name}")
                    break

        # Дополнительный fallback: ищем через анти-рекурсию
        if not source_node and anti_recursion_switch:
            for link in links:
                if link.to_node == anti_recursion_switch and link.to_socket.name == 'False':
                    source_node = link.from_node
                    source_socket = link.from_socket
                    print(f"[DEBUG] Найден источник через Anti-Recursion Switch: {source_node.name}")
                    break

        # Последний fallback: прямое подключение к выходу
        if not source_node:
            for link in links:
                if link.to_node == group_output and link.to_socket.name == 'Geometry':
                    source_node = link.from_node
                    source_socket = link.from_socket
                    print(f"[DEBUG] Найден источник через прямое подключение к выходу: {source_node.name}")
                    break

        if not source_node or not source_socket:
            print(f"[ERROR] apply_effector_to_collection_cloner: Не найден источник геометрии в клонере '{cloner_mod.name}'")
            return False

        # Проверяем, нет ли уже узла эффектора в группе
        effector_node_name = f"Effector_{effector_mod.name.replace(' ', '_')}"
        effector_node = None
        placeholder_effector = None

        for node in nodes:
            if node.name == effector_node_name:
                effector_node = node
                break
            elif node.name == "Effector_Random_Effector" and node.type == 'GROUP':
                placeholder_effector = node

        # Если узла эффектора нет, создаем его
        if not effector_node:
            if placeholder_effector:
                # Используем существующий placeholder и обновляем его
                effector_node = placeholder_effector
                effector_node.name = effector_node_name
                effector_node.node_tree = effector_group
                print(f"[DEBUG] Обновлен placeholder эффектор: {effector_node_name}")
            else:
                # Создаем новый узел Group
                effector_node = nodes.new('GeometryNodeGroup')
                effector_node.name = effector_node_name
                effector_node.node_tree = effector_group

                # Размещаем узел эффектора между источником и выходом
                if hasattr(source_node, 'location'):
                    effector_node.location = (
                        source_node.location[0] + 250,
                        source_node.location[1]
                    )

        # Удаляем существующие связи
        links_to_remove = []

        # Импортируем функцию для создания связей
        from .connection_management import safe_link_new

        # ПРАВИЛЬНАЯ ЛОГИКА на основе анализа BEFORE/AFTER файлов:
        # Эффектор должен быть между transform_geometry и final_realize_instances

        # Ищем узел final_realize_instances (это целевой узел для эффектора)
        final_realize_instances = None

        print(f"[DEBUG] Поиск final_realize_instances среди узлов:")
        print(f"[DEBUG] Всего узлов в группе: {len(nodes)}")

        realize_nodes = []
        for node in nodes:
            print(f"[DEBUG]   - Узел: '{node.name}' (тип: {node.type})")
            if node.type == 'GEOMETRY_NODE_REALIZE_INSTANCES':
                realize_nodes.append(node)
                print(f"[DEBUG]   ✅ Realize Instances узел: '{node.name}'")
                if ("final" in node.name.lower() or "realize" in node.name.lower()):
                    final_realize_instances = node
                    print(f"[DEBUG] ✅ Найден final_realize_instances: {node.name}")
                    break

        print(f"[DEBUG] Найдено Realize Instances узлов: {len(realize_nodes)}")

        # Если не нашли по имени, берем любой Realize Instances
        if not final_realize_instances:
            print(f"[DEBUG] Не найден по критерию 'final', используем realize_instances_node: {realize_instances_node.name if realize_instances_node else 'None'}")
            final_realize_instances = realize_instances_node

        if not final_realize_instances:
            print(f"[ERROR] Не найден узел final_realize_instances")
            return False

        # ТОЧНОЕ СЛЕДОВАНИЕ СХЕМЕ AFTER.py
        # Сначала находим все нужные узлы и связи

        # Ищем узел final_realize_switch
        final_realize_switch = None
        for node in nodes:
            if ("final_realize_switch" in node.name.lower() or
                ("final" in node.name.lower() and "switch" in node.name.lower())):
                final_realize_switch = node
                print(f"[DEBUG] Найден final_realize_switch: {node.name}")
                break

        # Ищем group_input
        group_input = None
        for node in nodes:
            if node.type == 'GROUP_INPUT':
                group_input = node
                break

        # НОВАЯ ЛОГИКА: Если используется placeholder, связи уже правильные
        # Нужно только обновить node_tree и проверить связи
        links_to_remove = []

        # Проверяем, используется ли placeholder (связи уже правильные)
        using_placeholder = (effector_node and effector_node.name == "Effector_Random_Effector" and
                           placeholder_effector is not None)

        if not using_placeholder:
            # Только если НЕ используется placeholder, удаляем старые связи

            # В новой схеме НЕ нужно удалять transform_geometry -> final_realize_instances!
            # Эта связь должна остаться (строка 512 в AFTER.py)

            # Удаляем только неправильные связи, если они есть:
            # 1. Удаляем transform_geometry -> final_realize_switch.Switch (из старой BEFORE схемы)
            if final_realize_switch:
                for link in links:
                    if (link.from_node == source_node and
                        link.to_node == final_realize_switch and
                        link.to_socket.name == 'Switch'):
                        links_to_remove.append(link)
                        print(f"[DEBUG] Удаляем неправильную связь: {source_node.name} -> {final_realize_switch.name}.Switch")
                        break

            # 2. Удаляем final_realize_instances -> final_realize_switch (если есть)
            if final_realize_switch:
                for link in links:
                    if (link.from_node == final_realize_instances and
                        link.to_node == final_realize_switch):
                        links_to_remove.append(link)
                        print(f"[DEBUG] Удаляем неправильную связь: {final_realize_instances.name} -> {final_realize_switch.name}")
                        break

            # Удаляем найденные связи
            for link in links_to_remove:
                links.remove(link)
        else:
            print(f"[DEBUG] Используется placeholder, связи уже правильные")

        # СОЗДАЕМ НОВЫЕ СВЯЗИ ТОЧНО ПО СХЕМЕ AFTER.py:
        try:
            if not using_placeholder:
                # Только если НЕ используется placeholder, создаем новые связи

                # НОВАЯ СХЕМА как в объектных клонерах (AFTER.py):
                # Шаг 1: transform_geometry -> effector (строка 514 в AFTER)
                safe_link_new(links, source_socket, effector_node.inputs['Geometry'])
                print(f"[DEBUG] ✅ Создана связь: {source_node.name} -> {effector_node.name}")

                # ВАЖНО: НЕ создаем effector -> final_realize_instances!
                # В новой схеме эффектор идет к final_realize_switch.False

            # Проверяем и создаем недостающие связи (даже для placeholder)

            # Убеждаемся, что transform_geometry -> final_realize_instances существует (строка 512 в AFTER)
            final_realize_connection_exists = False
            for link in links:
                if (link.from_node == source_node and
                    link.to_node == final_realize_instances and
                    link.to_socket.name == 'Geometry'):
                    final_realize_connection_exists = True
                    break

            if not final_realize_connection_exists:
                safe_link_new(links, source_socket, final_realize_instances.inputs['Geometry'])
                print(f"[DEBUG] ✅ Создана связь: {source_node.name} -> {final_realize_instances.name}")

            # Убеждаемся, что effector -> final_realize_switch.False существует (строка 516 в AFTER)
            effector_switch_connection_exists = False
            for link in links:
                if (link.from_node == effector_node and
                    link.to_node == final_realize_switch and
                    link.to_socket.name == 'False'):
                    effector_switch_connection_exists = True
                    break

            if not effector_switch_connection_exists:
                safe_link_new(links, effector_node.outputs['Geometry'], final_realize_switch.inputs['False'])
                print(f"[DEBUG] ✅ Создана связь: {effector_node.name} -> {final_realize_switch.name}.False")

            # ВАЖНО: В новой схеме НЕТ связи final_realize_instances -> final_realize_switch!
            # Final Realize Instances работает параллельно для анти-рекурсии

            # Шаг 4: group_input.Realize Instances -> final_realize_switch.Switch
            if final_realize_switch and group_input:
                # Ищем сокет "Realize Instances" в group_input
                realize_instances_output = None
                for output in group_input.outputs:
                    if "realize" in output.name.lower() and "instances" in output.name.lower():
                        realize_instances_output = output
                        break

                if realize_instances_output:
                    # Проверяем, нет ли уже такой связи
                    switch_connection_exists = False
                    for link in links:
                        if (link.from_node == group_input and
                            link.from_socket == realize_instances_output and
                            link.to_node == final_realize_switch and
                            link.to_socket.name == 'Switch'):
                            switch_connection_exists = True
                            break

                    if not switch_connection_exists:
                        safe_link_new(links, realize_instances_output, final_realize_switch.inputs['Switch'])
                        print(f"[DEBUG] ✅ Создана связь: group_input.Realize Instances -> {final_realize_switch.name}.Switch")

            # Шаг 5: Убеждаемся, что final_realize_switch.Output -> group_output (строка 533 в AFTER)
            if final_realize_switch:
                # Проверяем, есть ли связь к выходу
                output_connection_exists = False
                for link in links:
                    if (link.from_node == final_realize_switch and
                        link.to_node == group_output):
                        output_connection_exists = True
                        break

                if not output_connection_exists:
                    safe_link_new(links, final_realize_switch.outputs['Output'], group_output.inputs['Geometry'])
                    print(f"[DEBUG] ✅ Создана связь: {final_realize_switch.name}.Output -> group_output")

            # Активируем эффектор
            _set_effector_node_defaults(effector_node)

            print(f"[DEBUG] 🎯 Эффектор успешно интегрирован по схеме AFTER.py")

        except Exception as e:
            print(f"[ERROR] Ошибка при создании связей эффектора: {e}")
            import traceback
            traceback.print_exc()
            return False

        print(f"[DEBUG] apply_effector_to_collection_cloner: Эффектор '{effector_mod.name}' успешно применен к клонеру коллекций '{cloner_mod.name}'")
        return True

    except Exception as e:
        print(f"[ERROR] apply_effector_to_collection_cloner: {e}")
        import traceback
        traceback.print_exc()
        return False
