"""
Effector application utilities for the effector system.

This module handles the application of effectors to cloners,
copying parameters, and managing effector-specific logic.
"""

import bpy
from ....models.effectors import EFFECTOR_NODE_GROUP_PREFIXES
from .collection_effector import apply_effector_to_collection_cloner


def apply_effector_to_cloner(obj, cloner_mod, effector_mod):
    """
    Применяет эффектор к клонеру (общая функция для всех типов клонеров).

    Args:
        obj: Объект с клонером
        cloner_mod: Модификатор клонера
        effector_mod: Модификатор эффектора

    Returns:
        bool: True если эффектор успешно применен
    """
    try:
        # Проверяем, является ли клонер коллекционным
        is_collection_cloner = False
        if "source_type" in cloner_mod and cloner_mod["source_type"] == "COLLECTION":
            is_collection_cloner = True
        elif cloner_mod.node_group and cloner_mod.node_group.get("is_collection_cloner", False):
            is_collection_cloner = True
        elif cloner_mod.node_group and "CollectionCloner_" in cloner_mod.node_group.name:
            is_collection_cloner = True

        # Для клонеров коллекций используем специальную функцию
        if is_collection_cloner:
            print(f"[DEBUG] apply_effector_to_cloner: Применяем эффектор к клонеру коллекций {cloner_mod.name}")
            return apply_effector_to_collection_cloner(obj, cloner_mod, effector_mod)

        # Проверяем, является ли клонер стековым
        is_stacked = cloner_mod.get("is_stacked_cloner", False) or \
                    (cloner_mod.node_group and cloner_mod.node_group.get("is_stacked_cloner", False))

        if is_stacked:
            # Для стековых клонеров используем специальную функцию
            return apply_effector_to_stacked_cloner(obj, cloner_mod, effector_mod)
        else:
            # Для обычных клонеров используем стандартное обновление
            from .update_synchronization import update_cloner_with_effectors
            update_cloner_with_effectors(obj, cloner_mod)
            return True

    except Exception as e:
        print(f"[ERROR] apply_effector_to_cloner: {e}")
        return False


def copy_effector_parameters(effector_mod, effector_node):
    """
    Копирует параметры из модификатора эффектора в узел эффектора.

    Args:
        effector_mod: Модификатор эффектора
        effector_node: Узел эффектора в группе
    """
    try:
        effector_group = effector_mod.node_group
        for socket in effector_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                if socket.name == 'Geometry':
                    continue

                if socket.identifier in effector_mod:
                    try:
                        effector_node.inputs[socket.name].default_value = effector_mod[socket.identifier]
                        print(f"[DEBUG] Скопирован параметр {socket.name} = {effector_mod[socket.identifier]}")
                    except (KeyError, TypeError, AttributeError) as e:
                        print(f"[DEBUG] Не удалось скопировать параметр {socket.name}: {e}")
    except Exception as e:
        print(f"[DEBUG] Ошибка при копировании параметров эффектора: {e}")


def apply_effector_to_stacked_cloner(_, cloner_mod, effector_mod):
    """Применяет параметры эффектора к стековому клонеру

    Args:
        _: Неиспользуемый параметр (объект)
        cloner_mod: Модификатор стекового клонера
        effector_mod: Модификатор эффектора

    Returns:
        bool: True если эффектор успешно применен, False в случае ошибки
    """

    # Сохраняем важные настройки клонера чтобы они не потерялись
    cloner_settings = {}
    if cloner_mod.node_group:
        # Сохраняем тип клонера, чтобы его можно было восстановить
        if cloner_mod.get("cloner_type"):
            cloner_settings["cloner_type"] = cloner_mod["cloner_type"]
        elif cloner_mod.node_group.get("cloner_type"):
            cloner_settings["cloner_type"] = cloner_mod.node_group["cloner_type"]
            # Синхронизируем тип клонера
            cloner_mod["cloner_type"] = cloner_mod.node_group["cloner_type"]

        # Сохраняем флаг стекового клонера
        cloner_settings["is_stacked_cloner"] = True
        cloner_mod["is_stacked_cloner"] = True
        cloner_mod.node_group["is_stacked_cloner"] = True

        # Сохраняем важные параметры стекового клонера по типу
        cloner_type = cloner_settings.get("cloner_type", "")

        # Проверяем исходные имена параметров в клонере
        socket_params = {}
        for socket in cloner_mod.node_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                socket_params[socket.name] = socket.identifier

    # Фиксируем тип клонера, если он не определен
    if cloner_mod.get("is_stacked_cloner", False) and not cloner_mod.get("cloner_type"):
        # Определяем тип по имени группы
        node_group_name = cloner_mod.node_group.name
        if "_Grid_" in node_group_name or "Grid_Stack_" in node_group_name:
            cloner_mod["cloner_type"] = "GRID"
        elif "_Linear_" in node_group_name or "Linear_Stack_" in node_group_name:
            cloner_mod["cloner_type"] = "LINEAR"
        elif "_Circle_" in node_group_name or "Circle_Stack_" in node_group_name:
            cloner_mod["cloner_type"] = "CIRCLE"

    # Если тип клонера определен в node_group, но не в модификаторе, синхронизируем
    if not cloner_mod.get("cloner_type") and cloner_mod.node_group.get("cloner_type"):
        cloner_mod["cloner_type"] = cloner_mod.node_group["cloner_type"]

    # Активируем сокет Use Effector для стекового клонера
    # Это нужно для правильной работы эффекторов
    use_effector_activated = _activate_use_effector_socket(cloner_mod)

    # Если не удалось активировать Use Effector, пробуем найти его по индексу
    if not use_effector_activated:
        use_effector_activated = _activate_use_effector_by_index(cloner_mod)

    # Используем альтернативный подход, основанный на старой версии кода,
    # для более стабильной работы с эффекторами
    try:
        print(f"[DEBUG] apply_effector_to_stacked_cloner: Применение {effector_mod.name} к {cloner_mod.name} (старый метод)")

        # Проверяем наличие необходимых элементов
        if not cloner_mod.node_group or not effector_mod.node_group:
            print(f"[DEBUG] apply_effector_to_stacked_cloner: Нет node_group в клонере или эффекторе")
            return False

        # Получаем группы узлов
        cloner_group = cloner_mod.node_group
        effector_group = effector_mod.node_group

        # Проверяем наличие нужных входов/выходов в эффекторе
        input_sockets = [s.name for s in effector_group.interface.items_tree if s.item_type == 'SOCKET' and s.in_out == 'INPUT']
        output_sockets = [s.name for s in effector_group.interface.items_tree if s.item_type == 'SOCKET' and s.in_out == 'OUTPUT']

        # Убеждаемся, что у эффектора есть входы/выходы
        if 'Geometry' not in input_sockets or 'Geometry' not in output_sockets:
            print(f"[DEBUG] apply_effector_to_stacked_cloner: Эффектор не имеет нужных сокетов Geometry")
            return False

        # Проверяем, существует ли уже узел этого эффектора в клонере
        existing_effector_node = None
        for node in cloner_group.nodes:
            if node.name == f"Effector_{effector_mod.name}":
                existing_effector_node = node
                break

        # Если узел уже существует, обновляем его параметры
        if existing_effector_node:
            print(f"[DEBUG] apply_effector_to_stacked_cloner: Обновляем существующий узел эффектора")
            _update_existing_effector_node(existing_effector_node, effector_group, effector_mod)
            return True

        # Создаем новый узел эффектора
        print(f"[DEBUG] apply_effector_to_stacked_cloner: Создаем новый узел эффектора")
        return _create_new_effector_node(cloner_group, effector_group, effector_mod, cloner_mod)

    except Exception as e:
        print(f"[DEBUG] apply_effector_to_stacked_cloner: Ошибка при применении старого метода: {e}")
        import traceback
        traceback.print_exc()

        # Восстанавливаем прямую связь в случае ошибки
        try:
            # Используем функцию restore_direct_connection для восстановления связей
            from .connection_management import restore_direct_connection_improved
            restore_direct_connection_improved(cloner_mod.node_group)
            print(f"[DEBUG] apply_effector_to_stacked_cloner: Восстановлена прямая связь после ошибки")
        except Exception as restore_e:
            print(f"[DEBUG] apply_effector_to_stacked_cloner: Ошибка при восстановлении связи: {restore_e}")

        return False


def _activate_use_effector_socket(cloner_mod):
    """Активирует сокет Use Effector в клонере."""
    use_effector_activated = False
    try:
        # Найдем сокет Use Effector в интерфейсе клонера
        use_effector_socket = None
        for socket in cloner_mod.node_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and socket.name == "Use Effector":
                use_effector_socket = socket.identifier
                break

        # Если нашли сокет, активируем его
        if use_effector_socket:
            cloner_mod[use_effector_socket] = True
            use_effector_activated = True
            print(f"[DEBUG] Активирован сокет Use Effector ({use_effector_socket}) для {cloner_mod.name}")
        else:
            # Попробуем найти сокет по имени напрямую
            try:
                cloner_mod["Use Effector"] = True
                use_effector_activated = True
                print(f"[DEBUG] Активирован сокет Use Effector (прямой доступ) для {cloner_mod.name}")
            except Exception as inner_e:
                print(f"[DEBUG] Не найден сокет Use Effector для {cloner_mod.name}: {inner_e}")
    except Exception as e:
        print(f"[DEBUG] Ошибка при активации сокета Use Effector: {e}")

    return use_effector_activated


def _activate_use_effector_by_index(cloner_mod):
    """Активирует Use Effector по индексу."""
    use_effector_activated = False
    try:
        # Типичные индексы для Use Effector в разных типах клонеров
        common_indices = ["Socket_12", "Socket_13", "Socket_14", "Socket_15"]
        for idx in common_indices:
            try:
                if idx in cloner_mod:
                    current_val = cloner_mod[idx]
                    # Если это булево значение, вероятно это Use Effector
                    if isinstance(current_val, bool) or current_val in [0, 1]:
                        cloner_mod[idx] = True
                        use_effector_activated = True
                        print(f"[DEBUG] Активирован предполагаемый сокет Use Effector ({idx}) для {cloner_mod.name}")
                        break
            except:
                continue
    except Exception as e:
        print(f"[DEBUG] Ошибка при попытке активации Use Effector по индексу: {e}")

    return use_effector_activated


def _update_existing_effector_node(existing_effector_node, effector_group, effector_mod):
    """Обновляет существующий узел эффектора."""
    # Обновляем параметры
    for input_socket in [s for s in effector_group.interface.items_tree if s.item_type == 'SOCKET' and s.in_out == 'INPUT']:
        if input_socket.name in ['Geometry']:
            continue  # Пропускаем вход геометрии

        # Если параметр не имеет установленного значения в модификаторе, пропускаем
        if input_socket.identifier not in effector_mod:
            continue

        # Копируем значение параметра из модификатора в узел
        try:
            existing_effector_node.inputs[input_socket.name].default_value = effector_mod[input_socket.identifier]
        except (KeyError, TypeError) as e:
            print(f"[DEBUG] apply_effector_to_stacked_cloner: Не удалось установить значение для {input_socket.name}: {e}")
            pass


def _create_new_effector_node(cloner_group, effector_group, effector_mod, cloner_mod):
    """Создает новый узел эффектора в группе клонера."""
    # Найдем выходной узел и его входящую связь
    group_output = None
    for node in cloner_group.nodes:
        if node.type == 'GROUP_OUTPUT':
            group_output = node
            break

    if not group_output:
        print(f"[DEBUG] apply_effector_to_stacked_cloner: Нет выходного узла в клонере")
        return False

    # Найдем последний узел трансформации или первый с геометрией перед выходом
    source_node, source_socket = _find_source_node_for_effector(cloner_group, group_output)

    if not source_node:
        print(f"[DEBUG] apply_effector_to_stacked_cloner: Не найден источник геометрии в клонере")
        return False

    # Создаем новый узел эффектора
    effector_node = cloner_group.nodes.new('GeometryNodeGroup')
    effector_node.name = f"Effector_{effector_mod.name}"
    effector_node.node_tree = effector_group

    # Устанавливаем положение узла между источником и выходом
    source_pos = source_node.location
    output_pos = group_output.location
    effector_node.location = (source_pos.x + (output_pos.x - source_pos.x) * 0.5, source_pos.y)

    # Копируем значения параметров из модификатора эффектора
    copy_effector_parameters(effector_mod, effector_node)

    # Настраиваем связи
    _setup_effector_connections(cloner_group, effector_node, source_node, source_socket, group_output)

    # Отключаем рендер эффектора, т.к. его эффект уже применен через клонер
    effector_mod.show_render = False

    # Убедимся, что сохранены флаги стекового клонера
    cloner_mod["is_stacked_cloner"] = True
    cloner_group["is_stacked_cloner"] = True

    # Получаем и фиксируем тип клонера
    cloner_type = cloner_mod.get("cloner_type", "")
    if not cloner_type and cloner_group.get("cloner_type"):
        cloner_type = cloner_group["cloner_type"]

    if cloner_type:
        cloner_mod["cloner_type"] = cloner_type
        cloner_group["cloner_type"] = cloner_type

    print(f"[DEBUG] apply_effector_to_stacked_cloner: Эффектор успешно применен")
    return True


def _find_source_node_for_effector(cloner_group, group_output):
    """
    Находит исходный узел для подключения эффектора.

    КРИТИЧНО: При включенной анти-рекурсии эффектор ВСЕГДА должен быть между
    Transform Geometry и Realize Instances, независимо от типа клонера.
    """
    nodes = cloner_group.nodes
    links = cloner_group.links

    # Ищем ключевые узлы
    transform_geometry_node = None
    realize_instances_node = None
    anti_recursion_switch = None

    # Ищем узел Transform Geometry
    for node in nodes:
        if (node.type == 'GEOMETRY_NODE_TRANSFORM_GEOMETRY' or
            node.name == "Transform Geometry" or
            "Transform" in node.name):
            transform_geometry_node = node
            print(f"[DEBUG] Найден узел Transform Geometry: {node.name}")
            break

    # Ищем узел Realize Instances
    for node in nodes:
        if (node.type == 'GEOMETRY_NODE_REALIZE_INSTANCES' or
            node.name == "Anti-Recursion Realize" or
            "Realize" in node.name):
            realize_instances_node = node
            print(f"[DEBUG] Найден узел Realize Instances: {node.name}")
            break

    # Ищем узел Anti-Recursion Switch
    for node in nodes:
        if node.name == "Anti-Recursion Switch":
            anti_recursion_switch = node
            print(f"[DEBUG] Найден узел Anti-Recursion Switch: {node.name}")
            break

    # ОСНОВНАЯ ЛОГИКА: Эффектор ВСЕГДА между Transform Geometry и Realize Instances
    # На основе анализа BEFORE/AFTER файлов
    if transform_geometry_node and realize_instances_node:
        print(f"[DEBUG] Найдены Transform Geometry и Realize Instances - используем правильную схему")
        # Возвращаем Transform Geometry как источник для эффектора
        output_socket = None
        for output in transform_geometry_node.outputs:
            if output.type == 'GEOMETRY':
                output_socket = output
                break

        if output_socket:
            print(f"[DEBUG] Эффектор будет вставлен между {transform_geometry_node.name} и {realize_instances_node.name}")
            return transform_geometry_node, output_socket
        else:
            print(f"[ERROR] Не найден выход Geometry у узла Transform")

    # Fallback 1: Если есть Transform Geometry, но нет прямой связи с Realize
    if transform_geometry_node:
        print(f"[DEBUG] Используем Transform Geometry как источник (fallback)")
        output_socket = None
        for output in transform_geometry_node.outputs:
            if output.type == 'GEOMETRY':
                output_socket = output
                break
        if output_socket:
            return transform_geometry_node, output_socket

    # Fallback 2: Ищем любой узел с выходом Instances перед Realize
    if realize_instances_node:
        for link in links:
            if link.to_node == realize_instances_node and link.to_socket.name == 'Geometry':
                source_node = link.from_node
                if source_node != anti_recursion_switch:  # Не берем анти-рекурсию как источник
                    print(f"[DEBUG] Найден источник для Realize Instances: {source_node.name}")
                    return source_node, link.from_socket

    # Fallback 3: Стандартный поиск
    print(f"[DEBUG] Используем стандартный поиск узла")
    return _find_standard_source_node(nodes, group_output)


def _find_transform_node(nodes):
    """Находит узел трансформации в клонере."""
    # Ищем узел Transform или TransformGeometry
    for node in nodes:
        if 'Transform' in node.bl_idname and node.type != 'GROUP_OUTPUT':
            # Проверяем, что у него есть выход Geometry
            if 'Geometry' in [s.name for s in node.outputs]:
                return node, node.outputs['Geometry']

    # Если не нашли узел трансформации, ищем любой узел с выходом Geometry
    for node in nodes:
        if node.type not in ['GROUP_OUTPUT', 'GROUP_INPUT'] and not node.name.startswith('Effector_'):
            for output in node.outputs:
                if output.name == 'Geometry':
                    return node, output

    return None, None


def _find_standard_source_node(nodes, group_output):
    """Находит стандартный исходный узел."""
    # Сначала ищем узлы трансформации с выходом Geometry
    for node in nodes:
        if node != group_output and not node.name.startswith('Effector_'):
            for output in node.outputs:
                if output.name == 'Geometry' and any(link.to_node == group_output for link in output.links):
                    return node, output

    return None, None


def _setup_effector_connections(cloner_group, effector_node, source_node, source_socket, group_output):
    """
    Настраивает связи для эффектора.

    КРИТИЧНО: Эффектор ВСЕГДА должен быть между Transform Geometry и Realize Instances
    при включенной анти-рекурсии, независимо от типа клонера.
    """
    links = cloner_group.links
    nodes = cloner_group.nodes

    # Ищем ключевые узлы
    realize_instances_node = None
    anti_recursion_switch = None

    for node in nodes:
        if (node.type == 'GEOMETRY_NODE_REALIZE_INSTANCES' or
            node.name == "Anti-Recursion Realize" or
            "Realize" in node.name):
            realize_instances_node = node
            break

    for node in nodes:
        if node.name == "Anti-Recursion Switch":
            anti_recursion_switch = node
            break

    # Удаляем существующие связи от источника (Transform Geometry)
    links_to_remove = []
    target_node = None
    target_socket = None

    for link in links:
        if link.from_node == source_node and link.from_socket == source_socket:
            target_node = link.to_node
            target_socket = link.to_socket
            links_to_remove.append(link)
            print(f"[DEBUG] Найдена связь для удаления: {source_node.name} -> {target_node.name}")

    # Удаляем старые связи
    for link in links_to_remove:
        try:
            links.remove(link)
            print(f"[DEBUG] Удалена связь: {link.from_node.name} -> {link.to_node.name}")
        except RuntimeError:
            pass

    # ПРАВИЛЬНАЯ ЛОГИКА для стековых клонеров на основе анализа BEFORE/AFTER:
    # Эффектор должен быть между transform_geometry и realize_instances

    # Ищем целевой узел Realize Instances для эффектора
    target_realize_node = None
    if realize_instances_node:
        target_realize_node = realize_instances_node
    else:
        # Ищем любой Realize Instances в группе
        for node in nodes:
            if node.type == 'GEOMETRY_NODE_REALIZE_INSTANCES':
                target_realize_node = node
                break

    if not target_realize_node:
        print(f"[ERROR] Не найден узел Realize Instances для подключения эффектора")
        return

    # Ищем group_output для проверки финальных связей
    group_output_node = None
    for node in nodes:
        if node.type == 'GROUP_OUTPUT':
            group_output_node = node
            break

    try:
        # Шаг 1: Transform Geometry -> эффектор
        links.new(source_socket, effector_node.inputs['Geometry'])
        print(f"[DEBUG] ✅ Создана связь: {source_node.name} -> {effector_node.name}")

        # Шаг 2: эффектор -> Realize Instances
        links.new(effector_node.outputs['Geometry'], target_realize_node.inputs['Geometry'])
        print(f"[DEBUG] ✅ Создана связь: {effector_node.name} -> {target_realize_node.name}")

        # Шаг 3: Убеждаемся, что есть правильная связь к выходу
        if anti_recursion_switch and group_output_node:
            # Проверяем связь anti_recursion_switch -> group_output
            output_connection_exists = False
            for link in links:
                if (link.from_node == anti_recursion_switch and
                    link.to_node == group_output_node):
                    output_connection_exists = True
                    break

            if not output_connection_exists:
                links.new(anti_recursion_switch.outputs['Output'], group_output_node.inputs['Geometry'])
                print(f"[DEBUG] ✅ Создана связь: {anti_recursion_switch.name} -> group_output")

        elif target_realize_node and group_output_node:
            # Если нет анти-рекурсии, подключаем realize_instances -> group_output
            output_connection_exists = False
            for link in links:
                if (link.from_node == target_realize_node and
                    link.to_node == group_output_node):
                    output_connection_exists = True
                    break

            if not output_connection_exists:
                links.new(target_realize_node.outputs['Geometry'], group_output_node.inputs['Geometry'])
                print(f"[DEBUG] ✅ Создана связь: {target_realize_node.name} -> group_output")

        # Удаляем устаревший узел Effector_Input, если он есть
        effector_input_node = None
        for node in nodes:
            if node.name == "Effector_Input":
                effector_input_node = node
                break

        if effector_input_node:
            try:
                nodes.remove(effector_input_node)
                print(f"[DEBUG] Удален устаревший узел Effector_Input")
            except RuntimeError:
                pass

        print(f"[DEBUG] 🎯 Эффектор для стекового клонера успешно интегрирован")

    except Exception as e:
        print(f"[ERROR] Ошибка при настройке связей эффектора: {e}")
        import traceback
        traceback.print_exc()
        # В случае ошибки пытаемся восстановить базовую связь
        try:
            if target_node and target_socket:
                links.new(source_socket, target_socket)
                print(f"[DEBUG] Восстановлена базовая связь")
        except:
            pass
