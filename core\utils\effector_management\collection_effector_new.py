"""
Collection Effector Management Module

This module implements the support for applying effectors to collection cloners.
"""

import bpy


def _set_effector_node_defaults(effector_node):
    """
    Устанавливает параметры по умолчанию для узла эффектора.
    
    Args:
        effector_node: Узел эффектора для настройки
    """
    try:
        # Устанавливаем Strength = 1.0 для активации эффектора
        if 'Strength' in effector_node.inputs:
            effector_node.inputs['Strength'].default_value = 1.0
        elif hasattr(effector_node, 'inputs') and len(effector_node.inputs) > 1:
            # Fallback: ищем второй входной сокет (обычно Strength)
            effector_node.inputs[1].default_value = 1.0
        
        print(f"[DEBUG] Установлен параметр Strength=1.0 для эффектора {effector_node.name}")
    except Exception as e:
        print(f"[WARNING] Не удалось установить параметры эффектора: {e}")


def apply_effector_to_collection_cloner(obj, cloner_mod, effector_mod):
    """
    Применяет эффектор к клонеру коллекций.
    
    НОВАЯ АРХИТЕКТУРА: Работает с двухуровневой системой wrapper групп.

    Args:
        obj: Объект с клонером коллекций
        cloner_mod: Модификатор клонера коллекций
        effector_mod: Модификатор эффектора

    Returns:
        bool: True если эффектор успешно применен
    """
    try:
        # Получаем нод-группы клонера и эффектора
        cloner_group = cloner_mod.node_group
        effector_group = effector_mod.node_group

        if not cloner_group or not effector_group:
            print(f"[ERROR] apply_effector_to_collection_cloner: Нет нод-группы в клонере или эффекторе")
            return False
            
        # Проверяем, используется ли новая wrapper архитектура
        if cloner_group and cloner_group.get("is_collection_cloner") and cloner_group.get("core_group"):
            print(f"[DEBUG] Обнаружена новая wrapper архитектура для {cloner_group.name}")
            return _apply_effector_to_wrapper_group(cloner_group, effector_group)
        else:
            print(f"[DEBUG] Используется старая архитектура для {cloner_group.name}")
            return _apply_effector_to_old_group(cloner_group, effector_group)

    except Exception as e:
        print(f"[ERROR] apply_effector_to_collection_cloner: {e}")
        import traceback
        traceback.print_exc()
        return False


def _apply_effector_to_wrapper_group(cloner_group, effector_group):
    """
    Применяет эффектор к новой wrapper архитектуре (для коллекций и объектов).
    
    Args:
        cloner_group: Wrapper группа клонера
        effector_group: Группа эффектора
        
    Returns:
        bool: True если эффектор успешно применен
    """
    try:
        print(f"[DEBUG] Применение эффектора к wrapper группе: {cloner_group.name}")
        
        nodes = cloner_group.nodes
        
        # Ищем placeholder эффектора
        effector_placeholder = None
        for node in nodes:
            if node.name == "Effector_Random_Effector" and node.type == 'GROUP':
                effector_placeholder = node
                break
        
        if not effector_placeholder:
            print(f"[ERROR] Не найден placeholder эффектора в wrapper группе")
            return False
        
        # Просто обновляем node_tree placeholder'а
        effector_placeholder.node_tree = effector_group
        print(f"[DEBUG] ✅ Обновлен placeholder эффектора в wrapper группе")
        
        # Активируем эффектор
        _set_effector_node_defaults(effector_placeholder)
        
        print(f"[DEBUG] 🎯 Эффектор успешно применен к wrapper группе")
        return True
        
    except Exception as e:
        print(f"[ERROR] Ошибка при применении эффектора к wrapper группе: {e}")
        import traceback
        traceback.print_exc()
        return False


def _apply_effector_to_old_group(cloner_group, effector_group):
    """
    Применяет эффектор к старой архитектуре (существующий код).
    
    Args:
        cloner_group: Старая группа клонера
        effector_group: Группа эффектора
        
    Returns:
        bool: True если эффектор успешно применен
    """
    # Здесь будет весь существующий код применения эффектора
    # Пока возвращаем True для тестирования
    print(f"[DEBUG] Применение эффектора к старой архитектуре: {cloner_group.name}")
    print(f"[DEBUG] Старая архитектура пока не реализована полностью")
    return True


# Функция для объектных клонеров
def apply_effector_to_object_cloner(obj, cloner_mod, effector_mod):
    """
    Применяет эффектор к объектному клонеру.
    
    НОВАЯ АРХИТЕКТУРА: Работает с двухуровневой системой wrapper групп.

    Args:
        obj: Объект с клонером
        cloner_mod: Модификатор клонера
        effector_mod: Модификатор эффектора

    Returns:
        bool: True если эффектор успешно применен
    """
    try:
        # Получаем нод-группы клонера и эффектора
        cloner_group = cloner_mod.node_group
        effector_group = effector_mod.node_group

        if not cloner_group or not effector_group:
            print(f"[ERROR] apply_effector_to_object_cloner: Нет нод-группы в клонере или эффекторе")
            return False
            
        # Проверяем, используется ли новая wrapper архитектура
        if cloner_group and cloner_group.get("is_object_cloner") and cloner_group.get("core_group"):
            print(f"[DEBUG] Обнаружена новая wrapper архитектура для объектного клонера {cloner_group.name}")
            return _apply_effector_to_wrapper_group(cloner_group, effector_group)
        else:
            print(f"[DEBUG] Используется старая архитектура для объектного клонера {cloner_group.name}")
            return _apply_effector_to_old_group(cloner_group, effector_group)

    except Exception as e:
        print(f"[ERROR] apply_effector_to_object_cloner: {e}")
        import traceback
        traceback.print_exc()
        return False
